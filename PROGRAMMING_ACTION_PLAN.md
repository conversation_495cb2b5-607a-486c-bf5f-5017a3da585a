# ПРОГРАММНЫЙ ПЛАН ДЕЙСТВИЙ CORDON-82
## Конкретные функции и алгоритмы для решения проблем

---

## 🚨 КРИТИЧЕСКИЕ ИСПРАВЛЕНИЯ (НЕМЕДЛЕННО)

### **1. БЕЗОПАСНАЯ ФУНКЦИЯ M6 С ПРИНУДИТЕЛЬНОЙ ОСТАНОВКОЙ**

```c
// Замена существующей функции Rotate_M6_Step
void Rotate_M6_Step_Safe(uint8_t direction) {
    static uint16_t step_counter = 0;
    const uint16_t MAX_STEPS_PER_CALL = 50; // Максимум 50 шагов за вызов
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"M6: Safe step mode  ",20);
    
    Choose_M6;
    DD16_Enble;
    Delay_mS(5);
    Enable_Motor;
    
    // Установка направления
    if(direction == M6_Forward) {
        Rotate_CW;
    } else {
        Rotate_CCW;
    }
    
    for(uint16_t t = 0; t < 1000; t++) {} // Задержка стабилизации
    
    // БЕЗОПАСНОЕ ВРАЩЕНИЕ С КОНТРОЛЕМ
    for(uint16_t i = 0; i < MAX_STEPS_PER_CALL; i++) {
        // ПРОВЕРКА ДАТЧИКА ПЕРЕД КАЖДЫМ ШАГОМ
        if(!(D3)) {
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString((uint8_t *)"M6: PROJECTILE FOUND",20);
            goto safe_stop; // НЕМЕДЛЕННАЯ ОСТАНОВКА
        }
        
        // Один шаг мотора
        GPIOB->ODR |= GPIO_ODR_ODR0;
        Delay_mS(M6_StepDelay);
        GPIOB->ODR &= (~GPIO_ODR_ODR0);
        Delay_mS(M6_PulseWidth);
        
        step_counter++;
        
        // Проверка датчика после шага
        if(!(D3)) {
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString((uint8_t *)"M6: PROJECTILE FOUND",20);
            goto safe_stop; // НЕМЕДЛЕННАЯ ОСТАНОВКА
        }
        
        // Проверка D4 для подсчета снарядов
        if(!(D4)) {
            projectile_number += 1;
        }
    }
    
    // Если дошли до сюда - превышен лимит шагов
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"M6: STEP LIMIT HIT  ",20);
    
safe_stop:
    // ПРИНУДИТЕЛЬНАЯ ОСТАНОВКА
    Disable_Motor;
    DD16_Disble;
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"M6: SAFELY STOPPED  ",20);
    Delay_mS(250);
}
```

### **2. ФУНКЦИЯ УДЕРЖАНИЯ ПОЗИЦИИ M2**

```c
// Новая функция для удержания позиции M2
void M2_Hold_Position_Active(void) {
    static uint8_t hold_active = 0;
    static uint32_t last_hold_time = 0;
    
    if(!hold_active) {
        hold_active = 1;
        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString((uint8_t *)"M2: Position hold ON",20);
    }
    
    // Проверяем каждые 500мс
    if((Get_System_MS() - last_hold_time) > 500) {
        // Микроимпульс для удержания позиции
        Choose_M2;
        DD16_Enble;
        Enable_Motor;
        Rotate_CCW; // Направление удержания
        
        // Короткий импульс удержания
        GPIOB->ODR |= GPIO_ODR_ODR0;
        Delay_mS(M2_StepDelay_CCW);
        GPIOB->ODR &= (~GPIO_ODR_ODR0);
        Delay_mS(M2_PulseWidth_CCW);
        
        Disable_Motor;
        DD16_Disble;
        
        last_hold_time = Get_System_MS();
    }
}

// Функция отключения удержания
void M2_Hold_Position_Stop(void) {
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"M2: Position hold OFF",20);
    // Логика отключения будет в основном цикле
}
```

### **3. АДАПТИВНАЯ СКОРОСТЬ M3 С ЗАЩИТОЙ**

```c
// Функция с автоматическим снижением скорости при проблемах
void Rotate_M3_Adaptive(uint8_t direction) {
    static uint16_t current_delay_us = 800; // Начинаем с безопасного значения
    static uint8_t error_count = 0;
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"M3: Adaptive mode   ",20);
    
    Choose_M3;
    DD16_Enble;
    Delay_mS(5);
    Enable_Motor;
    
    for(uint16_t t = 0; t < 1000; t++) {}
    
    // Установка направления
    if(direction == M3_Forward) {
        Rotate_CCW;
    } else {
        Rotate_CW;
    }
    
    for(uint16_t t = 0; t < 1000; t++) {}
    
    uint8_t movement_successful = 1;
    
    while(1) {
        // Проверка датчиков перед движением
        if(direction == M3_Forward && !(D2)) {
            break; // Достигли D2
        }
        if(direction == M3_Back && !(D1)) {
            break; // Достигли D1
        }
        
        // Попытка шага с текущей скоростью
        GPIOB->ODR |= GPIO_ODR_ODR0;
        Delay_uS(current_delay_us);
        GPIOB->ODR &= (~GPIO_ODR_ODR0);
        Delay_uS(current_delay_us);
        
        // Проверка на зависание (простая проверка)
        static uint32_t step_start_time = 0;
        step_start_time = Get_System_MS();
        
        // Если шаг занял слишком много времени - ошибка
        if((Get_System_MS() - step_start_time) > 100) {
            error_count++;
            movement_successful = 0;
            
            // Снижаем скорость при ошибках
            if(current_delay_us < 2000) {
                current_delay_us += 200; // Увеличиваем задержку = снижаем скорость
                LCD_Send_Command(LCD_4_LINE_POS_0);
                LCD_SendString((uint8_t *)"M3: Speed reduced   ",20);
            }
            
            if(error_count > 5) {
                LCD_Send_Command(LCD_4_LINE_POS_0);
                LCD_SendString((uint8_t *)"M3: TOO MANY ERRORS ",20);
                break;
            }
        }
    }
    
    Disable_Motor;
    DD16_Disble;
    
    if(movement_successful && error_count == 0) {
        // Если все прошло хорошо, можем попробовать ускориться
        if(current_delay_us > 600) {
            current_delay_us -= 50;
        }
    }
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"M3: Current %d us   ", current_delay_us);
    Delay_mS(500);
}
```

---

## 📊 СИСТЕМА ДИАГНОСТИКИ И МОНИТОРИНГА

### **4. СТРУКТУРЫ ДАННЫХ ДЛЯ МОНИТОРИНГА**

```c
// Глобальные переменные для мониторинга
typedef struct {
    uint8_t motor_id;
    uint32_t total_steps;
    uint32_t successful_operations;
    uint32_t failed_operations;
    uint16_t current_speed_hz;
    uint8_t health_percentage;
    uint8_t last_error_code;
    uint32_t last_operation_time_ms;
    uint8_t is_active;
    uint8_t protection_triggered;
} Motor_Monitor_t;

Motor_Monitor_t motor_monitor[8]; // Индексы 1-7 для M1-M7

// Коды ошибок
#define ERROR_NONE              0
#define ERROR_TIMEOUT           1
#define ERROR_SENSOR_FAIL       2
#define ERROR_DRIVER_PROTECTION 3
#define ERROR_SPEED_TOO_HIGH    4
#define ERROR_POSITION_LOST     5

// Инициализация мониторинга
void Init_Motor_Monitoring(void) {
    for(uint8_t i = 1; i <= 7; i++) {
        motor_monitor[i].motor_id = i;
        motor_monitor[i].health_percentage = 100;
        motor_monitor[i].last_error_code = ERROR_NONE;
        motor_monitor[i].is_active = 0;
        motor_monitor[i].protection_triggered = 0;
    }
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Motor monitoring ON ",20);
}
```

### **5. ФУНКЦИИ МОНИТОРИНГА**

```c
// Обновление статистики мотора
void Update_Motor_Stats(uint8_t motor_id, uint8_t success, uint32_t operation_time) {
    if(motor_id < 1 || motor_id > 7) return;
    
    Motor_Monitor_t* mon = &motor_monitor[motor_id];
    
    mon->total_steps++;
    mon->last_operation_time_ms = operation_time;
    
    if(success) {
        mon->successful_operations++;
        mon->last_error_code = ERROR_NONE;
    } else {
        mon->failed_operations++;
    }
    
    // Расчет здоровья мотора
    if(mon->total_steps > 0) {
        mon->health_percentage = (mon->successful_operations * 100) / mon->total_steps;
    }
    
    // Если здоровье критично низкое
    if(mon->health_percentage < 50) {
        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString((uint8_t *)"M%d: CRITICAL HEALTH", motor_id);
        mon->protection_triggered = 1;
    }
}

// Отображение статуса всех моторов
void Show_All_Motors_Status(void) {
    for(uint8_t i = 1; i <= 7; i++) {
        Motor_Monitor_t* mon = &motor_monitor[i];
        
        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString((uint8_t *)"M%d: %d%% HP %d err  ", 
                      i, mon->health_percentage, mon->failed_operations);
        Delay_mS(1000);
    }
}

// Проверка критических проблем
uint8_t Check_Critical_Motor_Issues(void) {
    uint8_t critical_issues = 0;
    
    for(uint8_t i = 1; i <= 7; i++) {
        Motor_Monitor_t* mon = &motor_monitor[i];
        
        // Критические условия
        if(mon->health_percentage < 30) {
            critical_issues++;
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString((uint8_t *)"M%d: CRITICAL!      ", i);
            Delay_mS(500);
        }
        
        if(mon->failed_operations > 10) {
            critical_issues++;
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString((uint8_t *)"M%d: TOO MANY FAILS ", i);
            Delay_mS(500);
        }
    }
    
    return critical_issues;
}
```

---

## 🔧 УЛУЧШЕННЫЙ СЦЕНАРИЙ READY

### **6. НОВЫЙ БЕЗОПАСНЫЙ READY_COMMAND**

```c
void Ready_Command_Safe(void) {
    // Инициализация
    SensorPositionError = 0;
    Timer_Start();
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== SAFE READY ===  ",20);
    
    // Проверка критических проблем перед стартом
    if(Check_Critical_Motor_Issues() > 0) {
        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString((uint8_t *)"CRITICAL ISSUES!    ",20);
        SensorPositionError = 1;
        return;
    }
    
    Timer_Stop_And_Show("CHECKPOINT 1: SAFE START");
    
    // M6 ИСКЛЮЧЕН - начинаем с проверки M3
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"M6 EXCLUDED - M3 CHK",20);
    
    // Проверка позиции M3
    if(D1) {
        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString((uint8_t *)"M3: Moving to D1... ",20);
        
        uint32_t start_time = Get_System_MS();
        Rotate_M3_Adaptive(M3_Back);
        uint32_t operation_time = Get_System_MS() - start_time;
        
        Update_Motor_Stats(3, !(D1), operation_time); // Успех если достигли D1
    }
    
    Timer_Stop_And_Show("CHECKPOINT 2: M3 READY");
    
    // Основной сценарий с мониторингом
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== SAFE LOADING === ",20);
    
    // Строка 1: M3->D2 M5->D5 с мониторингом
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Line 1: M3+M5 move  ",20);
    
    uint32_t m3_start = Get_System_MS();
    Rotate_M3_Adaptive(M3_Forward);
    Update_Motor_Stats(3, !(D2), Get_System_MS() - m3_start);
    
    uint32_t m5_start = Get_System_MS();
    Rotate_M5(M5_Back);
    Update_Motor_Stats(5, !(D5), Get_System_MS() - m5_start);
    
    // Строка 2: M4->D7 с мониторингом
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Line 2: M4->D7      ",20);
    
    uint32_t m4_start = Get_System_MS();
    Rotate_M4(M4_Back);
    Update_Motor_Stats(4, !(D7), Get_System_MS() - m4_start);
    
    Timer_Stop_And_Show("CHECKPOINT 3: MOTORS OK");
    
    // Строка 3: M7->D11
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Line 3: M7->D11     ",20);
    
    uint32_t m7_start = Get_System_MS();
    Rotate_M7(M7_Back);
    Update_Motor_Stats(7, !M7_Error, Get_System_MS() - m7_start);
    
    Timer_Stop_And_Show("CHECKPOINT 4: GRIP ON");
    
    // Активация удержания позиции M2
    M2_Hold_Position_Active();
    
    // Продолжение сценария...
    // (остальные строки аналогично с мониторингом)
    
    // Финальная проверка
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Final: Health check ",20);
    
    Show_All_Motors_Status();
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== SAFE READY DONE!",20);
    
    // Отключение удержания M2
    M2_Hold_Position_Stop();
}
```

---

## 🎯 ФУНКЦИИ АВТОКАЛИБРОВКИ

### **7. АВТОМАТИЧЕСКИЙ ПОИСК ОПТИМАЛЬНЫХ СКОРОСТЕЙ**

```c
void Auto_Calibrate_Motor_Speed(uint8_t motor_id) {
    if(motor_id < 1 || motor_id > 6) return;
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"Calibrating M%d...  ", motor_id);
    
    uint16_t min_delay = 100;   // Минимальная задержка (максимальная скорость)
    uint16_t max_delay = 2000;  // Максимальная задержка (минимальная скорость)
    uint16_t optimal_delay = max_delay;
    
    // Бинарный поиск оптимальной скорости
    while(max_delay - min_delay > 50) {
        uint16_t test_delay = (min_delay + max_delay) / 2;
        
        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString((uint8_t *)"M%d: Testing %d us  ", motor_id, test_delay);
        
        if(Test_Motor_Speed_Safe(motor_id, test_delay)) {
            // Скорость работает, пробуем быстрее
            max_delay = test_delay;
            optimal_delay = test_delay;
        } else {
            // Скорость не работает, пробуем медленнее
            min_delay = test_delay;
        }
        
        Delay_mS(500);
    }
    
    // Сохранение оптимальной скорости
    Save_Motor_Optimal_Speed(motor_id, optimal_delay);
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString((uint8_t *)"M%d: Optimal %d us  ", motor_id, optimal_delay);
    Delay_mS(1000);
}

uint8_t Test_Motor_Speed_Safe(uint8_t motor_id, uint16_t delay_us) {
    // Безопасный тест скорости мотора
    // Возвращает 1 если скорость работает, 0 если нет
    
    uint8_t success = 1;
    uint16_t test_steps = 10; // Небольшое количество шагов для теста
    
    // Выбор мотора и инициализация
    switch(motor_id) {
        case 3:
            Choose_M3;
            break;
        case 4:
            Choose_M4;
            break;
        case 5:
            Choose_M5;
            break;
        default:
            return 0; // Неподдерживаемый мотор
    }
    
    DD16_Enble;
    Enable_Motor;
    Rotate_CW; // Тестовое направление
    
    // Тестовые шаги
    for(uint16_t i = 0; i < test_steps; i++) {
        uint32_t step_start = Get_System_MS();
        
        GPIOB->ODR |= GPIO_ODR_ODR0;
        Delay_uS(delay_us);
        GPIOB->ODR &= (~GPIO_ODR_ODR0);
        Delay_uS(delay_us);
        
        uint32_t step_time = Get_System_MS() - step_start;
        
        // Если шаг занял слишком много времени - проблема
        if(step_time > 50) {
            success = 0;
            break;
        }
    }
    
    Disable_Motor;
    DD16_Disble;
    
    return success;
}
```

---

## 📈 СИСТЕМА ОТЧЕТНОСТИ

### **8. ГЕНЕРАЦИЯ ОТЧЕТОВ О РАБОТЕ**

```c
void Generate_Motor_Report(void) {
    LCD_Send_Command(LCD_CLEAR_POS_0);
    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== MOTOR REPORT ===",20);
    
    for(uint8_t i = 1; i <= 7; i++) {
        Motor_Monitor_t* mon = &motor_monitor[i];
        
        LCD_Send_Command(LCD_2_LINE_POS_0);
        LCD_SendString((uint8_t *)"Motor M%d:          ", i);
        
        LCD_Send_Command(LCD_3_LINE_POS_0);
        LCD_SendString((uint8_t *)"Health: %d%% (%d/%d)", 
                      mon->health_percentage,
                      mon->successful_operations,
                      mon->total_steps);
        
        LCD_Send_Command(LCD_4_LINE_POS_0);
        if(mon->last_error_code == ERROR_NONE) {
            LCD_SendString((uint8_t *)"Status: OK          ");
        } else {
            LCD_SendString((uint8_t *)"Last Error: %d      ", mon->last_error_code);
        }
        
        Delay_mS(2000); // Показываем каждый мотор 2 секунды
    }
    
    // Общая статистика
    LCD_Send_Command(LCD_CLEAR_POS_0);
    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString((uint8_t *)"=== SUMMARY ===     ",20);
    
    uint8_t healthy_motors = 0;
    uint8_t critical_motors = 0;
    
    for(uint8_t i = 1; i <= 7; i++) {
        if(motor_monitor[i].health_percentage > 80) {
            healthy_motors++;
        } else if(motor_monitor[i].health_percentage < 50) {
            critical_motors++;
        }
    }
    
    LCD_Send_Command(LCD_2_LINE_POS_0);
    LCD_SendString((uint8_t *)"Healthy: %d/7       ", healthy_motors);
    
    LCD_Send_Command(LCD_3_LINE_POS_0);
    LCD_SendString((uint8_t *)"Critical: %d/7      ", critical_motors);
    
    LCD_Send_Command(LCD_4_LINE_POS_0);
    if(critical_motors == 0) {
        LCD_SendString((uint8_t *)"System: READY       ");
    } else {
        LCD_SendString((uint8_t *)"System: MAINTENANCE ");
    }
    
    Delay_mS(3000);
}
```

---

*Программный план готов к реализации*
*Приоритет: Критические функции -> Мониторинг -> Автокалибровка*
