# ТЕХНИЧЕСКИЙ ПЛАН ДЕЙСТВИЙ CORDON-82
## Пошаговое руководство по устранению проблем моторов

---

## 🚨 НЕМЕДЛЕННЫЕ ДЕЙСТВИЯ (СЕГОДНЯ)

### **ШАГ 1: ДИАГНОСТИКА M6 (КРИТИЧНО)**
**Время: 2-4 часа | Приоритет: МАКСИМАЛЬНЫЙ**

#### **1.1 Проверка датчика D3:**
```bash
# Подключить мультиметр к пину D3
# Проверить напряжение:
# - Без мины: должно быть HIGH (3.3V или 5V)
# - С миной: должно быть LOW (0V)
```

**Действия:**
1. Отключить питание системы
2. Подключить мультиметр к пину D3 (см. схему подключения)
3. Включить питание
4. Поместить мину в активную секцию барабана
5. Проверить изменение сигнала

**Ожидаемый результат:** Четкое переключение HIGH/LOW

#### **1.2 Проверка механизма барабана:**
1. **Визуальный осмотр:**
   - Нет ли заедания при ручном вращении
   - Правильно ли установлен датчик D3
   - Нет ли повреждений зубчатой передачи

2. **Тест ручного вращения:**
   - Отключить мотор M6
   - Вручную повернуть барабан
   - Убедиться что датчик D3 срабатывает в нужной позиции

#### **1.3 Проверка драйвера M6:**
```c
// Добавить в код диагностический режим
void Test_M6_Driver(void) {
    LCD_SendString("Testing M6 driver...");
    
    // Тест Enable/Disable
    Enable_Motor;
    Delay_mS(1000);
    Disable_Motor;
    
    // Тест направления
    Rotate_CW;
    Delay_mS(500);
    Rotate_CCW;
    Delay_mS(500);
    
    LCD_SendString("M6 driver test done");
}
```

### **ШАГ 2: УСИЛЕНИЕ M2 (ВЫСОКИЙ ПРИОРИТЕТ)**
**Время: 1-2 часа | Приоритет: ВЫСОКИЙ**

#### **2.1 Проверка настроек драйвера:**
1. **Ток драйвера:**
   - Найти потенциометр настройки тока на драйвере M2
   - Измерить текущее значение
   - Увеличить ток на 20-30% (осторожно!)

2. **Микрошаги:**
   - Проверить настройки микрошагов (DIP-переключатели)
   - Попробовать уменьшить микрошаги для увеличения крутящего момента
   - Рекомендуется: 1/4 или 1/2 микрошага вместо 1/16

#### **2.2 Механическая проверка:**
1. **Люфты:**
   - Проверить крепление мотора M2
   - Проверить соединение с механизмом подъема
   - Устранить люфты в соединениях

2. **Смазка:**
   - Смазать подшипники механизма подъема
   - Проверить легкость хода без мотора

#### **2.3 Программная оптимизация:**
```c
// Добавить режим удержания позиции
void M2_Hold_Position(void) {
    // Периодические микроимпульсы для удержания
    while(position_hold_active) {
        GPIOB->ODR |= GPIO_ODR_ODR0;
        Delay_mS(M2_StepDelay_CCW);
        GPIOB->ODR &= (~GPIO_ODR_ODR0);
        Delay_mS(M2_PulseWidth_CCW);
        
        Delay_mS(100); // Пауза между импульсами
    }
}
```

---

## 📋 СРЕДНЕСРОЧНЫЕ ДЕЙСТВИЯ (НА ЭТОЙ НЕДЕЛЕ)

### **ШАГ 3: ОПТИМИЗАЦИЯ M3**
**Время: 1 час | Приоритет: СРЕДНИЙ**

#### **3.1 Настройка драйвера:**
1. **Ток драйвера:**
   - Проверить настройку тока драйвера M3
   - Возможно уменьшить ток для предотвращения перегрева

2. **Охлаждение:**
   - Установить радиатор на драйвер M3
   - Улучшить вентиляцию в корпусе

#### **3.2 Тестирование скоростей:**
```c
// Автоматический поиск максимальной безопасной скорости
void Find_M3_Max_Speed(void) {
    uint16_t test_delay = 1000; // Начинаем с 1мс
    
    while(test_delay > 100) { // До 100мкс
        M3_StepDelay_uS = test_delay;
        M3_PulseWidth_uS = test_delay;
        
        if(Test_M3_Stability()) {
            LCD_SendString("M3 stable at %d us", test_delay);
            break;
        }
        
        test_delay -= 50; // Уменьшаем на 50мкс
    }
}
```

### **ШАГ 4: ПРОГРАММНЫЕ УЛУЧШЕНИЯ**
**Время: 4-6 часов | Приоритет: СРЕДНИЙ**

#### **4.1 Безопасная остановка M6:**
```c
void Rotate_M6_Safe_Step(uint8_t direction) {
    static uint16_t safety_counter = 0;
    const uint16_t MAX_STEPS = 1000; // Максимум шагов
    
    Choose_M6;
    DD16_Enble;
    Enable_Motor;
    
    // Установка направления
    if(direction == M6_Forward) {
        Rotate_CW;
    } else {
        Rotate_CCW;
    }
    
    // Безопасное вращение с контролем
    while(safety_counter < MAX_STEPS) {
        // Один шаг
        GPIOB->ODR |= GPIO_ODR_ODR0;
        Delay_mS(M6_StepDelay);
        GPIOB->ODR &= (~GPIO_ODR_ODR0);
        Delay_mS(M6_PulseWidth);
        
        safety_counter++;
        
        // КРИТИЧЕСКАЯ ПРОВЕРКА ДАТЧИКА
        if(!(D3)) {
            LCD_SendString("M6: Projectile detected!");
            break; // НЕМЕДЛЕННАЯ ОСТАНОВКА
        }
        
        // Проверка каждые 10 шагов
        if(safety_counter % 10 == 0) {
            LCD_SendString("M6: Step %d", safety_counter);
        }
    }
    
    // ПРИНУДИТЕЛЬНАЯ ОСТАНОВКА
    Disable_Motor;
    DD16_Disble;
    safety_counter = 0;
    
    if(safety_counter >= MAX_STEPS) {
        LCD_SendString("M6: TIMEOUT ERROR!");
        SensorPositionError = 1;
    }
}
```

#### **4.2 Диагностическая система:**
```c
typedef struct {
    uint8_t motor_id;
    uint32_t total_steps;
    uint32_t successful_runs;
    uint32_t error_count;
    uint8_t last_error_code;
    uint32_t last_run_duration;
    uint8_t health_status; // 0-100%
} Motor_Diagnostics_t;

Motor_Diagnostics_t motor_stats[7];

void Update_Motor_Stats(uint8_t motor_id, uint8_t success, uint32_t duration) {
    motor_stats[motor_id].total_steps++;
    
    if(success) {
        motor_stats[motor_id].successful_runs++;
    } else {
        motor_stats[motor_id].error_count++;
    }
    
    motor_stats[motor_id].last_run_duration = duration;
    
    // Расчет здоровья мотора
    uint32_t success_rate = (motor_stats[motor_id].successful_runs * 100) / 
                           motor_stats[motor_id].total_steps;
    motor_stats[motor_id].health_status = success_rate;
}

void Show_Motor_Diagnostics(void) {
    for(uint8_t i = 1; i <= 6; i++) {
        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString("M%d: %d%% health", i, motor_stats[i].health_status);
        Delay_mS(1000);
    }
}
```

---

## 🔧 ДОЛГОСРОЧНЫЕ УЛУЧШЕНИЯ (В ТЕЧЕНИЕ МЕСЯЦА)

### **ШАГ 5: АВТОКАЛИБРОВКА**
```c
void Auto_Calibrate_All_Motors(void) {
    LCD_SendString("Starting auto-calibration...");
    
    // Калибровка каждого мотора
    for(uint8_t motor = 1; motor <= 6; motor++) {
        if(motor == 6) continue; // Пропускаем проблемный M6
        
        LCD_SendString("Calibrating M%d...", motor);
        
        uint16_t optimal_speed = Find_Optimal_Speed(motor);
        Save_Motor_Speed(motor, optimal_speed);
        
        LCD_SendString("M%d: %d Hz optimal", motor, optimal_speed);
        Delay_mS(1000);
    }
    
    LCD_SendString("Calibration complete!");
}

uint16_t Find_Optimal_Speed(uint8_t motor_id) {
    uint16_t start_speed = 100;  // Начальная скорость
    uint16_t max_speed = 5000;   // Максимальная скорость
    uint16_t step = 100;         // Шаг увеличения
    
    for(uint16_t speed = start_speed; speed <= max_speed; speed += step) {
        if(Test_Motor_At_Speed(motor_id, speed)) {
            // Скорость работает, пробуем быстрее
            continue;
        } else {
            // Скорость не работает, возвращаем предыдущую
            return speed - step;
        }
    }
    
    return max_speed; // Если все скорости работают
}
```

### **ШАГ 6: ПРЕДИКТИВНОЕ ОБСЛУЖИВАНИЕ**
```c
typedef struct {
    uint32_t total_runtime_hours;
    uint32_t total_cycles;
    uint8_t temperature_max;
    uint8_t vibration_level;
    uint8_t maintenance_needed; // 0-100%
} Motor_Health_t;

void Check_Maintenance_Needs(void) {
    for(uint8_t i = 1; i <= 6; i++) {
        Motor_Health_t* health = &motor_health[i];
        
        // Анализ износа
        if(health->total_cycles > 100000) {
            health->maintenance_needed += 20;
        }
        
        if(health->temperature_max > 70) {
            health->maintenance_needed += 30;
        }
        
        if(health->maintenance_needed > 80) {
            LCD_SendString("M%d needs maintenance!", i);
            Generate_Maintenance_Alert(i);
        }
    }
}
```

---

## 📊 КОНТРОЛЬНЫЕ ТОЧКИ

### **Через 1 день:**
- [ ] M6 корректно останавливается при обнаружении мины
- [ ] M2 надежно держит позицию ствола
- [ ] Сценарий READY выполняется без ошибок

### **Через 1 неделю:**
- [ ] M3 работает на максимальной безопасной скорости
- [ ] Добавлена диагностическая система
- [ ] Все моторы имеют защиту от зависания

### **Через 1 месяц:**
- [ ] Автокалибровка всех моторов
- [ ] Предиктивное обслуживание
- [ ] Статистика работы и отчеты

---

## ⚠️ МЕРЫ БЕЗОПАСНОСТИ

1. **Перед любыми работами:**
   - Отключить питание системы
   - Убедиться что все моторы остановлены
   - Использовать антистатические меры

2. **При работе с драйверами:**
   - Не превышать максимальный ток
   - Контролировать температуру
   - Иметь запасные драйверы

3. **При тестировании:**
   - Начинать с минимальных скоростей
   - Постоянно контролировать состояние
   - Иметь кнопку аварийной остановки

---

*План подготовлен инженерной службой CORDON-82*
*Статус: ГОТОВ К ВЫПОЛНЕНИЮ*
