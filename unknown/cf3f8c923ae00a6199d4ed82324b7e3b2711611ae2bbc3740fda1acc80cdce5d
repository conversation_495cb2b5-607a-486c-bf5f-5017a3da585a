# Инструкция по настройке SD карты

## Файлы конфигурации моторов

Система поддерживает два типа файлов конфигурации:

### 1. **motor_config.txt** (основной файл)
- **Расположение**: корень SD карты
- **Формат**: текстовый, удобный для редактирования
- **Кодировка**: UTF-8 или ASCII
- **Размер**: ~3-5 KB

### 2. **motor_config.bin** (бинарный файл)
- **Расположение**: корень SD карты  
- **Формат**: бинарный, создается автоматически
- **Размер**: точно 256 байт

## Структура SD карты

```
SD_CARD_ROOT/
├── motor_config.txt     <- ОСНОВНОЙ файл конфигурации
├── motor_config.bin     <- Бинарный файл (создается автоматически)
├── logs/                <- Папка для логов (опционально)
└── backup/              <- Папка для резервных копий (опционально)
```

## Как использовать

### Способ 1: Редактирование на компьютере
1. Извлеките SD карту из устройства
2. Вставьте в компьютер
3. Отредактируйте файл `motor_config.txt` любым текстовым редактором
4. Сохраните файл
5. Вставьте SD карту обратно в устройство
6. Отправьте UART команду: `$<96><00><00><00><00>;`
7. Система загрузит новые параметры

### Способ 2: Через UART команды
1. Отправьте команду `$<97><00><00><00><00>;` для сохранения текущих параметров
2. Система создаст файл `motor_config.txt` на SD карте
3. Извлеките карту и отредактируйте файл
4. Загрузите обратно командой `$<96><00><00><00><00>;`

## UART команды

| Команда | Hex код | Описание |
|---------|---------|----------|
| Загрузить конфигурацию | `$<96><00><00><00><00>;` | Читает motor_config.txt с SD |
| Сохранить конфигурацию | `$<97><00><00><00><00>;` | Записывает motor_config.txt на SD |
| Сценарий READY | `$<07><00><00><00><00>;` | Выполняет основной сценарий |
| Тест M6 | `$<98><00><00><00><00>;` | Тест мотора M6 на макс. мощности |
| Тест всех моторов | `$<99><00><00><00><00>;` | Тест всех моторов |

## Кнопки

| Кнопки | Действие |
|--------|----------|
| SW1+SW2 | Сценарий READY |
| SW1 | Тест мотора M1 |
| SW2 | Тест мотора M2 |
| SW3 | Тест мотора M3 |
| SW4 | Тест мотора M4 |
| SW5 | Тест мотора M5 |
| SW6 | Тест мотора M6 |

## Параметры моторов

### Формат строки в motor_config.txt:
```
M1=step_delay,pulse_width,max_speed,accel,invert,enable
```

Где:
- **step_delay**: задержка между шагами в микросекундах (10-10000)
- **pulse_width**: длительность импульса в микросекундах (5-5000)
- **max_speed**: максимальная скорость в шагах/сек (100-50000)
- **accel**: включить разгон (1=да, 0=нет)
- **invert**: инвертировать направление (1=да, 0=нет)
- **enable**: включить мотор (1=да, 0=нет)

### Примеры настроек:

**Высокая скорость (10 кГц):**
```
M1=50,25,10000,0,0,1
```

**Средняя скорость (5 кГц) - по умолчанию:**
```
M1=100,50,5000,0,0,1
```

**Максимальная мощность для M6:**
```
M6=200,100,2500,0,0,1
```

## Устранение проблем

### Мотор пищит и не крутится
- Увеличьте `step_delay` до 100-200 мкс
- Пример: `M1=150,75,3333,0,0,1`

### Мотор не тянет нагрузку (особенно M6)
- Уменьшите `step_delay` до 50-100 мкс
- Уменьшите `pulse_width` до 25-50 мкс
- Пример: `M6=100,50,5000,0,0,1`

### Система не загружает конфигурацию
- Проверьте, что файл называется точно `motor_config.txt`
- Проверьте, что файл в корне SD карты
- Проверьте синтаксис файла (нет лишних пробелов)

## Резервное копирование

Рекомендуется создавать резервные копии рабочих конфигураций:

```
SD_CARD_ROOT/backup/
├── motor_config_v1.0.txt
├── motor_config_v1.1.txt
└── motor_config_field_tested.txt
```

## Безопасность

⚠️ **ВНИМАНИЕ:**
- Не устанавливайте `step_delay` меньше 10 мкс
- Для M6 минимальное значение `step_delay` = 50 мкс
- Всегда тестируйте новые параметры командой 99 перед использованием
- Сохраняйте рабочие конфигурации в папке backup
