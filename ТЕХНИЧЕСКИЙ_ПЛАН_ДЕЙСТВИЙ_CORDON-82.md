# ТЕХНИЧЕСКИЙ ПЛАН ДЕЙСТВИЙ CORDON-82
## Пошаговое руководство по устранению проблем моторов

**Версия документа:** 2.0
**Дата создания:** 10/06/2025
**Статус:** ГОТОВ К ВЫПОЛНЕНИЮ
**Приоритет:** КРИТИЧЕСКИЙ

---

## 🚨 КРИТИЧЕСКИЕ ПРОБЛЕМЫ МОТОРОВ

### **M1 - МОТОР ГОРИЗОНТАЛЬНОЙ НАВОДКИ**
**Технические характеристики:**
- **Тип:** NEMA23 шаговый мотор
- **Крутящий момент:** 3 Н·м
- **Редуктор:** Планетарный 1:40
- **Энкодер:** Абсолютный 10-битный
- **Выходной момент:** 120 Н·м (3 × 40)

**Статус:** ✅ **РАБОТАЕТ СТАБИЛЬНО**

**Проблемы:**
- Изначально медленная скорость (100 Гц)
- Недоиспользование потенциала мотора

**Решение:**
- Ускорен до 500 Гц (увеличение в 5 раз)
- Оптимизация алгоритма управления

**Текущие настройки:**
- StepDelay: 1мс
- PulseWidth: 1мс
- Частота: 500 Гц
- Микрошаги: 1/16

**Результат:** Работает без проблем, достаточная мощность для точного позиционирования

**Технические улучшения:**
- Возможность увеличения до 1000 Гц при улучшении охлаждения драйвера
- Реализация S-образного профиля скорости для плавного разгона/торможения
- Добавление компенсации люфта редуктора

---

### **M2 - МОТОР ВЕРТИКАЛЬНОЙ НАВОДКИ**
**Технические характеристики:**
- **Тип:** NEMA34 шаговый мотор
- **Крутящий момент:** 8.5 Н·м
- **Редуктор:** Планетарный 1:10
- **Энкодер:** Абсолютный 10-битный
- **Выходной момент:**  Н·м (12.5 × 10)
- **Нагрузка:** Ствол миномета (~150 кг)

**Статус:** ⚠️ **ПРОБЛЕМЫ С УДЕРЖАНИЕМ ПОЗИЦИИ**

**Проблемы:**
1. При ускорении до 70 Гц дуло иногда падает после подъема из-за отсуцтвия тормоза
2. Редуктор планетарного типа не имеет встроенного тормоза
3. Недостаточная мощность удержания при отключении питания
4. Не закреплен кронштейн крепления мотора
5. Требует больше времени на импульс для генерации крутящего момента

**Решения:**
1. **Программные:**
   - Возврат к мощным настройкам 50 Гц (10мс задержки)
   - Реализация режима удержания позиции с микроимпульсами
   - Увеличение тока драйвера на 20-30%


2. **Механические:**
   - Установка пружины для стабилизации нагрузки
   - Закрепление кронштейна крепления мотора
   - Установка электромагнитного или др. тормоза (опционально)

**Текущие настройки:**
- StepDelay_CW: 10мс
- StepDelay_CCW: 10мс
- PulseWidth_CCW: 10мс
- ExtraDelay_CCW: 10мс
- Частота: 50 Гц

**Результат:** Держит позицию надежно, но медленнее поднимает

**Технические улучшения:**
- Установка датчика нагрузки для адаптивного управления током (опционально)
- Реализация PID-регулятора для точного позиционирования (опционально)
- Добавление системы противовеса для снижения нагрузки на мотор ()реализация почти невозможна, но как опциональный вариант пускай будет

---

### **M3 - МОТОР КАРЕТКИ**
**Технические характеристики:**
- **Тип:** NEMA23 шаговый мотор
- **Передача:** Цепная передача
- **Ход каретки:** ~1250мм
- **Датчики:** D1 (нижнее положение), D2 (верхнее положение)

**Статус:** ⚠️ **ПРОБЛЕМЫ СКОРОСТИ**

**Проблемы:**
1. При скорости 20000 Гц (50мкс) не крутится вообще
2. При скорости 5000 Гц (200мкс) драйвер уходит в защиту
3. Механизм сцепления на высокой скорости не срабатывает
4. Слишком высокая частота импульсов для данного драйвера
5. Возможно чрезмерное натяжение цепи

**Решения:**
1. **Программные:**
   - Снижение до безопасных 1250 Гц (800мкс) но скорость очень печальная. 
   - Как вариант замена редуктора на менее мощный, например 1:4, 1:10
   - Реализация адаптивного управления скоростью
   - Добавление защиты от перегрузки

2. **Механические:**
   - Регулировка натяжения цепи
   - Смазка цепи и механизмов
   - Улучшение механизма сцепления каретки
   - Установка более мощного драйвера (опционально)

**Текущие настройки:**
- StepDelay_uS: 800мкс
- PulseWidth_uS: 800мкс
- Частота: 1250 Гц

**Результат:** Работает стабильно, в 12 раз быстрее оригинала, но в 5 раз медленнее идеального

**Технические улучшения:**
- Замена драйвера на более мощный (например, DM860A) (требуется обсуждение)
- Установка датчика тока для мониторинга нагрузки(опционально)


---

### **M4 - МОТОР ПРОДОЛЬНОГО ПЕРЕМЕЩЕНИЯ**
**Технические характеристики:**
- **Тип:** NEMA23 шаговый мотор
- **Передача:** Зубчатая передача
- **Ход:** ~500мм
- **Датчики:** D7, D8, D9 (позиционирование)

**Статус:** ✅ **РАБОТАЕТ ОПТИМАЛЬНО**

**Проблемы:**
1. В задвинутом состоянии цепляет датчики и механизмы
2. Шкивами цепляет ящик блока управления при поднятом M2
3. Изначально медленная скорость, писк при высоких частотах ✅(устранено)

**Решения:**
1. **Механические:**
   - Перенести датчики в недосягаемое место
   - Перенести ящик блока управления
   - Установка защитных кожухов (опционально)

2. **Программные:**
   - Оптимизация до 2000 Гц (500мкс)
   - Добавление проверок безопасности

**Текущие настройки:**
- StepDelay_uS: 500мкс
- PulseWidth_uS: 500мкс
- Частота: 2000 Гц

**Результат:** Быстро, не пищит, стабильно

**Технические улучшения:**
- Установка концевых выключателей с большим ходом
- Реализация плавного торможения перед препятствиями
- Добавление системы автоматического позиционирования

---

### **M5 - МОТОР ВРАЩЕНИЯ МЕХАНИЗМА ЗАГРУЗКИ**
**Технические характеристики:**
- **Тип:** NEMA17 шаговый мотор
- **Передача:** Ременная передача
- **Угол поворота:** 180°
- **Датчики:** D5, D6 (крайние положения)

**Статус:** ✅ **РАБОТАЕТ ОПТИМАЛЬНО**

**Проблемы:**
1. Упор в неправильном положении
2. Не дает возможности довернуть захват до среза дула
3. Изначально медленная скорость, писк при высоких частотах ✅(устранено)

**Решения:**
1. **Механические:**
   - Переставить упор в правильное положение
   - Регулировка концевых датчиков

2. **Программные:**
   - Оптимизация до 1667 Гц (600мкс)
   - Точная калибровка углов поворота

**Текущие настройки:**
- StepDelay_uS: 600мкс
- PulseWidth_uS: 600мкс
- Частота: 1667 Гц

**Результат:** Быстро, не пищит, стабильно

**Технические улучшения:**
- Реализация программных концевиков
- Добавление системы автоматической калибровки углов

---

### **M6 - МОТОР ВРАЩЕНИЯ БАРАБАНА**
**Технические характеристики:**
- **Тип:** NEMA17 шаговый мотор
- **Передача:** Ременная передача 1:3
- **Нагрузка:** Барабан с 6 минами (~27 кг)
- **Датчики:** D3 (наличие мины), D4 (счетчик оборотов)

**Статус:** 🚫 **КРИТИЧЕСКАЯ ПРОБЛЕМА - НЕ ОСТАНАВЛИВАЕТСЯ**

**Проблемы:**
1. **КРИТИЧНО:** Не останавливается при достижении датчика D3
2. Мощность мотора впритык для вращения нагруженного барабана
3. Не отрабатывают датчики наличия мины (программная ошибка)
4. Может крутиться бесконечно, повреждая механизм
5. Неправильная логика проверки датчиков в коде

**Технический анализ проблемы:**
```c
// ОШИБКА В КОДЕ: проверяется D4 вместо D3!
while(1) {
    // Шаги мотора...
    if(!(D4)) {  // ❌ НЕПРАВИЛЬНЫЙ ДАТЧИК!
        break;
    }
    // НЕТ ТАЙМАУТА - БЕСКОНЕЧНЫЙ ЦИКЛ!
}
```

**Решения:**
1. **Программные (КРИТИЧНО):**
   - Исправить проверку датчика с D4 на D3
   - Добавить таймаут защиты (максимум 1000 шагов)
   - Реализация безопасной функции остановки

2. **Технические:**
   - Проверка и калибровка датчика D3
   - Увеличение мощности драйвера
   - Установка более мощного мотора (NEMA23)

**Статус:** ТРЕБУЕТ НЕМЕДЛЕННОЙ ДИАГНОСТИКИ

**Результат:** Остановка при срабатывании D3, защита от зависаний, проверка датчика D3

---

### **M7 - DC МОТОР ЗАХВАТА**
**Технические характеристики:**
- **Тип:** DC мотор с редуктором
- **Напряжение:** 12V
- **Ток:** 2A
- **Редуктор:** 1:30
- **Датчики:** D10 (открыто), D11 (закрыто)

**Статус:** ✅ **РАБОТАЕТ СТАБИЛЬНО**

**Проблемы:** Нет критических проблем

**Особенности:**
- Использует датчики D10/D11 для позиционирования
- Таймаут защиты 10 секунд
- Контроль силы зажима

**Результат:** Работает с таймаутами и проверками для удержания силы зажима

**Технические улучшения:**
- Установка датчика тока для контроля силы зажима
- Реализация адаптивного управления силой
- Добавление системы автоматической калибровки силы

---

## 🚨 БЕЗОТЛАГАТЕЛЬНЫЕ ДЕЙСТВИЯ

### **ШАГ 1: ДИАГНОСТИКА M6 (КРИТИЧНО)**
**Время выполнения:** 2-4 часа
**Приоритет:** МАКСИМАЛЬНЫЙ
**Ответственный:** Ведущий инженер

#### **1.1 Проверка датчика D3**

**Необходимое оборудование:**
- Цифровой мультиметр
- Осциллограф (желательно)
- Тестовые мины

**Процедура проверки:**
```bash
# Пошаговая диагностика датчика D3
1. Отключить питание системы
2. Подключить мультиметр к пину D3 (см. схему подключения)
3. Включить питание
4. Измерить напряжение без мины: должно быть HIGH (3.3V или 5V)
5. Поместить мину в активную секцию барабана
6. Измерить напряжение с миной: должно быть LOW (0V)
7. Проверить стабильность сигнала при вибрации
```

**Ожидаемый результат:** Четкое переключение HIGH/LOW без дребезга

**Критерии успеха:**
- Разность напряжений > 2.5V
- Время переключения < 10мс
- Отсутствие ложных срабатываний

#### **1.2 Проверка механизма барабана**

**Визуальный осмотр:**
1. Проверить отсутствие заедания при ручном вращении
2. Убедиться в правильной установке датчика D3
3. Проверить состояние ременной передачи
4. Осмотреть крепления и подшипники

**Тест ручного вращения:**
1. Отключить мотор M6 от драйвера
2. Вручную повернуть барабан на полный оборот
3. Убедиться что датчик D3 срабатывает в каждой позиции мины
4. Проверить плавность вращения

**Измерение параметров:**
- Момент сопротивления вращению: < 2 Н·м
- Люфт в передаче: < 2°
- Время срабатывания датчика: < 50мс

#### **1.3 Проверка драйвера M6**

**Диагностический код:**
```c
void Test_M6_Driver(void) {
    LCD_SendString("Testing M6 driver...");

    // Тест Enable/Disable
    Enable_Motor;
    Delay_mS(1000);
    Disable_Motor;

    // Тест направления
    Rotate_CW;
    Delay_mS(500);
    Rotate_CCW;
    Delay_mS(500);

    // Тест импульсов
    for(int i = 0; i < 100; i++) {
        GPIOB->ODR |= GPIO_ODR_ODR0;
        Delay_uS(500);
        GPIOB->ODR &= (~GPIO_ODR_ODR0);
        Delay_uS(500);
    }

    LCD_SendString("M6 driver test done");
}
```

**Проверяемые параметры:**
- Напряжение питания драйвера: 24V ±5%
- Ток потребления: < 3A
- Температура драйвера: < 60°C
- Частота импульсов: 500-2000 Гц

---

### **ШАГ 2: УСИЛЕНИЕ M2 (ВЫСОКИЙ ПРИОРИТЕТ)**
**Время выполнения:** 1-2 часа
**Приоритет:** ВЫСОКИЙ
**Ответственный:** Механик + Программист

#### **2.1 Настройка драйвера**

**Регулировка тока:**
1. Найти потенциометр настройки тока на драйвере M2
2. Измерить текущее значение тока (должно быть ~2.5A)
3. Постепенно увеличить ток на 20-30% (до 3.2A максимум)
4. Контролировать температуру драйвера

**Настройка микрошагов:**
- Текущая настройка: 1/16 микрошага
- Рекомендуемая: 1/4 или 1/2 микрошага
- Цель: увеличение крутящего момента в 2-4 раза

**Таблица настроек DIP-переключателей:**
| Микрошаги | SW1 | SW2 | SW3 | Момент | Точность |
|-----------|-----|-----|-----|--------|----------|
| 1/2       | OFF | OFF | OFF | 100%   | Низкая   |
| 1/4       | ON  | OFF | OFF | 85%    | Средняя  |
| 1/8       | OFF | ON  | OFF | 70%    | Высокая  |
| 1/16      | ON  | ON  | OFF | 50%    | Очень высокая |

#### **2.2 Механические улучшения**

**Закрепление кронштейна:**
1. Проверить все болтовые соединения
2. Использовать фиксатор резьбы (Loctite 243)
3. Момент затяжки: 15-20 Н·м

**Установка пружины стабилизации:**
- Тип: Газовая пружина 200N
- Установка: Параллельно стволу
- Цель: Компенсация 40-50% веса ствола

**Смазка механизмов:**
- Подшипники: Литиевая смазка NLGI 2
- Направляющие: Тефлоновая смазка
- Периодичность: Каждые 1000 циклов

#### **2.3 Программная оптимизация**

**Режим удержания позиции:**
```c
void M2_Hold_Position(void) {
    static uint32_t last_hold_time = 0;

    // Проверяем каждые 500мс
    if((Get_System_MS() - last_hold_time) > 500) {
        // Микроимпульс для удержания
        Choose_M2;
        DD16_Enble;
        Enable_Motor;
        Rotate_CCW;

        // Короткий импульс
        GPIOB->ODR |= GPIO_ODR_ODR0;
        Delay_mS(M2_StepDelay_CCW);
        GPIOB->ODR &= (~GPIO_ODR_ODR0);
        Delay_mS(M2_PulseWidth_CCW);

        Disable_Motor;
        DD16_Disble;

        last_hold_time = Get_System_MS();
    }
}
```

**Адаптивное управление током:**
```c
void M2_Adaptive_Current(uint8_t load_level) {
    // load_level: 0-100%
    uint16_t base_delay = 10; // мс
    uint16_t adaptive_delay = base_delay + (load_level / 10);

    M2_StepDelay_CCW = adaptive_delay;
    M2_PulseWidth_CCW = adaptive_delay;
}
```

---

## 📋 СРЕДНЕСРОЧНЫЕ ДЕЙСТВИЯ

### **ШАГ 3: ОПТИМИЗАЦИЯ M3**
**Время выполнения:** 1-2 часа
**Приоритет:** СРЕДНИЙ
**Ответственный:** Инженер-электронщик

#### **3.1 Настройка драйвера**

**Проблемы:**
- Механизм сцепления на высокой скорости не срабатывает
- Слишком высокая частота импульсов
- Драйвер часто уходит в защиту

**Регулировка тока драйвера:**
1. Проверить текущую настройку тока драйвера M3
2. При необходимости уменьшить ток на 10-15% для предотвращения перегрева
3. Оптимальный ток: 2.0-2.5A для NEMA23

**Улучшение охлаждения:**
- Установить алюминиевый радиатор 40x40x20мм на драйвер M3
- Обеспечить воздушный поток 0.5 м/с
- Контроль температуры: < 55°C

#### **3.2 Механические решения**

**Регулировка натяжения цепи:**
1. Ослабить натяжение цепи на 20-30%
2. Проверить плавность хода каретки
3. Оптимальное натяжение: прогиб 5-8мм на середине пролета

**Смазка и обслуживание:**
- Смазать цепь специальной смазкой для цепей
- Очистить направляющие каретки
- Проверить износ звездочек и роликов

**Улучшение механизма сцепления:**
- Установить демпфер для плавного сцепления
- Отрегулировать зазоры в механизме
- Добавить пружинную компенсацию

#### **3.3 Программная оптимизация**

**Автоматический поиск оптимальной скорости:**
```c
void Find_M3_Max_Speed(void) {
    uint16_t test_delay = 1000; // Начинаем с 1мс
    uint8_t stability_test_passed = 0;

    LCD_SendString("M3: Speed calibration...");

    while(test_delay > 100) { // До 100мкс
        M3_StepDelay_uS = test_delay;
        M3_PulseWidth_uS = test_delay;

        // Тест стабильности на 100 шагах
        if(Test_M3_Stability(100)) {
            LCD_SendString("M3: %d us - STABLE", test_delay);
            stability_test_passed = 1;
            break;
        } else {
            LCD_SendString("M3: %d us - UNSTABLE", test_delay);
        }

        test_delay += 50; // Увеличиваем задержку
    }

    if(stability_test_passed) {
        Save_M3_Optimal_Speed(test_delay);
        LCD_SendString("M3: Optimal speed saved");
    } else {
        LCD_SendString("M3: Using safe defaults");
        M3_StepDelay_uS = 800;
        M3_PulseWidth_uS = 800;
    }
}

uint8_t Test_M3_Stability(uint16_t test_steps) {
    uint8_t errors = 0;
    uint32_t start_time = Get_System_MS();

    Choose_M3;
    DD16_Enble;
    Enable_Motor;
    Rotate_CW;

    for(uint16_t i = 0; i < test_steps; i++) {
        uint32_t step_start = Get_System_MS();

        GPIOB->ODR |= GPIO_ODR_ODR0;
        Delay_uS(M3_StepDelay_uS);
        GPIOB->ODR &= (~GPIO_ODR_ODR0);
        Delay_uS(M3_PulseWidth_uS);

        // Проверка времени выполнения шага
        uint32_t step_time = Get_System_MS() - step_start;
        if(step_time > 10) { // Шаг занял больше 10мс
            errors++;
        }

        // Проверка на пропуск шагов (по энкодеру, если есть)
        // if(Check_Step_Loss()) errors++;
    }

    Disable_Motor;
    DD16_Disble;

    uint32_t total_time = Get_System_MS() - start_time;
    uint8_t success_rate = ((test_steps - errors) * 100) / test_steps;

    LCD_SendString("M3: %d%% success, %dms", success_rate, total_time);

    return (success_rate > 95); // Успех если > 95%
}
```

---

### **ШАГ 4: ПРОГРАММНЫЕ УЛУЧШЕНИЯ**
**Время выполнения:** 4-6 часов
**Приоритет:** СРЕДНИЙ
**Ответственный:** Программист

#### **4.1 Безопасная остановка M6**

**Критическое исправление логики:**
```c
void Rotate_M6_Safe_Step(uint8_t direction) {
    static uint16_t safety_counter = 0;
    const uint16_t MAX_STEPS = 1000; // Максимум шагов за вызов
    const uint16_t MAX_TOTAL_STEPS = 5000; // Абсолютный максимум

    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString("M6: Safe rotation...");

    Choose_M6;
    DD16_Enble;
    Enable_Motor;

    // Установка направления
    if(direction == M6_Forward) {
        Rotate_CW;
    } else {
        Rotate_CCW;
    }

    // Стабилизация
    for(uint16_t t = 0; t < 1000; t++) {}

    // Безопасное вращение с множественным контролем
    for(uint16_t i = 0; i < MAX_STEPS; i++) {
        // КРИТИЧЕСКАЯ ПРОВЕРКА ДАТЧИКА D3 ПЕРЕД ШАГОМ
        if(!(D3)) {
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString("M6: Projectile detected!");
            goto safe_stop;
        }

        // Один шаг мотора
        GPIOB->ODR |= GPIO_ODR_ODR0;
        Delay_mS(M6_StepDelay);
        GPIOB->ODR &= (~GPIO_ODR_ODR0);
        Delay_mS(M6_PulseWidth);

        safety_counter++;

        // КРИТИЧЕСКАЯ ПРОВЕРКА ДАТЧИКА D3 ПОСЛЕ ШАГА
        if(!(D3)) {
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString("M6: Projectile found!");
            goto safe_stop;
        }

        // Проверка D4 для подсчета снарядов
        if(!(D4)) {
            projectile_number += 1;
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString("M6: Projectile #%d", projectile_number);
        }

        // Проверка общего лимита шагов
        if(safety_counter > MAX_TOTAL_STEPS) {
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString("M6: TOTAL LIMIT HIT!");
            SensorPositionError = 1;
            goto safe_stop;
        }

        // Индикация прогресса каждые 10 шагов
        if(i % 10 == 0) {
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString("M6: Step %d/%d", i, MAX_STEPS);
        }
    }

    // Если дошли до сюда - превышен лимит шагов за вызов
    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString("M6: STEP LIMIT HIT");

safe_stop:
    // ПРИНУДИТЕЛЬНАЯ ОСТАНОВКА
    Disable_Motor;
    DD16_Disble;

    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString("M6: SAFELY STOPPED");
    Delay_mS(250);

    // Сброс счетчика только при успешном завершении
    if(!(D3)) {
        safety_counter = 0;
    }
}
```

#### **4.2 Система диагностики моторов**

**Структуры данных для мониторинга:**
```c
typedef struct {
    uint8_t motor_id;
    uint32_t total_steps;
    uint32_t successful_runs;
    uint32_t error_count;
    uint8_t last_error_code;
    uint32_t last_run_duration;
    uint8_t health_status;        // 0-100%
    uint16_t current_speed_hz;
    uint8_t temperature;          // °C
    uint16_t vibration_level;     // 0-1000
    uint32_t total_runtime_hours;
} Motor_Diagnostics_t;

// Коды ошибок
#define ERROR_NONE              0
#define ERROR_TIMEOUT           1
#define ERROR_SENSOR_FAIL       2
#define ERROR_DRIVER_PROTECTION 3
#define ERROR_SPEED_TOO_HIGH    4
#define ERROR_POSITION_LOST     5
#define ERROR_OVERHEATING       6
#define ERROR_OVERCURRENT       7

Motor_Diagnostics_t motor_stats[8]; // M1-M7 + резерв

void Init_Motor_Monitoring(void) {
    for(uint8_t i = 1; i <= 7; i++) {
        motor_stats[i].motor_id = i;
        motor_stats[i].health_status = 100;
        motor_stats[i].last_error_code = ERROR_NONE;
        motor_stats[i].current_speed_hz = 0;
        motor_stats[i].temperature = 25;
        motor_stats[i].vibration_level = 0;
        motor_stats[i].total_runtime_hours = 0;
    }

    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString("Motor monitoring ON");
}

void Update_Motor_Stats(uint8_t motor_id, uint8_t success, uint32_t duration) {
    if(motor_id < 1 || motor_id > 7) return;

    Motor_Diagnostics_t* mon = &motor_stats[motor_id];

    mon->total_steps++;
    mon->last_run_duration = duration;

    if(success) {
        mon->successful_runs++;
        mon->last_error_code = ERROR_NONE;
    } else {
        mon->error_count++;
    }

    // Расчет здоровья мотора
    if(mon->total_steps > 0) {
        mon->health_status = (mon->successful_runs * 100) / mon->total_steps;
    }

    // Обновление времени работы
    mon->total_runtime_hours += duration / 3600000; // мс в часы

    // Проверка критических состояний
    if(mon->health_status < 50) {
        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString("M%d: CRITICAL HEALTH!", motor_id);
        mon->last_error_code = ERROR_POSITION_LOST;
    }

    if(mon->temperature > 70) {
        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString("M%d: OVERHEATING!", motor_id);
        mon->last_error_code = ERROR_OVERHEATING;
    }
}

void Show_Motor_Diagnostics(void) {
    for(uint8_t i = 1; i <= 7; i++) {
        Motor_Diagnostics_t* mon = &motor_stats[i];

        LCD_Send_Command(LCD_1_LINE_POS_0);
        LCD_SendString("=== MOTOR M%d ===", i);

        LCD_Send_Command(LCD_2_LINE_POS_0);
        LCD_SendString("Health: %d%% (%d/%d)",
                      mon->health_status,
                      mon->successful_runs,
                      mon->total_steps);

        LCD_Send_Command(LCD_3_LINE_POS_0);
        LCD_SendString("Speed: %d Hz, T: %d°C",
                      mon->current_speed_hz,
                      mon->temperature);

        LCD_Send_Command(LCD_4_LINE_POS_0);
        if(mon->last_error_code == ERROR_NONE) {
            LCD_SendString("Status: OK");
        } else {
            LCD_SendString("Error: %d", mon->last_error_code);
        }

        Delay_mS(2000);
    }
}
```

---

## 🔧 ДОЛГОСРОЧНЫЕ УЛУЧШЕНИЯ

### **ШАГ 5: АВТОКАЛИБРОВКА СИСТЕМЫ**
**Время выполнения:** 8-12 часов
**Приоритет:** НИЗКИЙ
**Ответственный:** Ведущий программист

#### **5.1 Автоматическая калибровка скоростей**

```c
void Auto_Calibrate_All_Motors(void) {
    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString("=== AUTO CALIBRATION ===");

    // Калибровка каждого мотора
    for(uint8_t motor = 1; motor <= 6; motor++) {
        if(motor == 6) {
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString("M6: Skipped (unsafe)");
            continue; // Пропускаем проблемный M6
        }

        LCD_Send_Command(LCD_2_LINE_POS_0);
        LCD_SendString("Calibrating M%d...", motor);

        uint16_t optimal_speed = Find_Optimal_Speed(motor);
        Save_Motor_Speed(motor, optimal_speed);

        LCD_Send_Command(LCD_3_LINE_POS_0);
        LCD_SendString("M%d: %d Hz optimal", motor, optimal_speed);

        // Тест надежности на найденной скорости
        uint8_t reliability = Test_Motor_Reliability(motor, optimal_speed);

        LCD_Send_Command(LCD_4_LINE_POS_0);
        LCD_SendString("M%d: %d%% reliable", motor, reliability);

        Delay_mS(2000);
    }

    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString("Calibration complete!");
}

uint16_t Find_Optimal_Speed(uint8_t motor_id) {
    uint16_t start_speed = 100;   // Начальная скорость (Гц)
    uint16_t max_speed = 5000;    // Максимальная скорость (Гц)
    uint16_t step = 100;          // Шаг увеличения (Гц)
    uint16_t optimal_speed = start_speed;

    for(uint16_t speed = start_speed; speed <= max_speed; speed += step) {
        LCD_Send_Command(LCD_3_LINE_POS_0);
        LCD_SendString("Testing %d Hz...", speed);

        if(Test_Motor_At_Speed(motor_id, speed)) {
            optimal_speed = speed;
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString("%d Hz - OK", speed);
        } else {
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString("%d Hz - FAIL", speed);
            break; // Скорость не работает, возвращаем предыдущую
        }

        Delay_mS(500);
    }

    return optimal_speed;
}

uint8_t Test_Motor_At_Speed(uint8_t motor_id, uint16_t speed_hz) {
    // Конвертация частоты в задержку (мкс)
    uint32_t delay_us = 1000000 / (speed_hz * 2); // *2 для полного цикла

    uint8_t success = 1;
    uint16_t test_steps = 50; // Количество тестовых шагов

    // Выбор и инициализация мотора
    switch(motor_id) {
        case 1: Choose_M1; break;
        case 2: Choose_M2; break;
        case 3: Choose_M3; break;
        case 4: Choose_M4; break;
        case 5: Choose_M5; break;
        default: return 0;
    }

    DD16_Enble;
    Enable_Motor;
    Rotate_CW;

    // Тестовые шаги с контролем времени
    for(uint16_t i = 0; i < test_steps; i++) {
        uint32_t step_start = Get_System_MS();

        GPIOB->ODR |= GPIO_ODR_ODR0;
        Delay_uS(delay_us);
        GPIOB->ODR &= (~GPIO_ODR_ODR0);
        Delay_uS(delay_us);

        uint32_t step_time = Get_System_MS() - step_start;

        // Проверка времени выполнения
        uint32_t expected_time = (delay_us * 2) / 1000; // мс
        if(step_time > (expected_time + 5)) { // +5мс допуск
            success = 0;
            break;
        }
    }

    Disable_Motor;
    DD16_Disble;

    return success;
}
```

#### **5.2 Предиктивное обслуживание**

```c
typedef struct {
    uint32_t total_runtime_hours;
    uint32_t total_cycles;
    uint8_t temperature_max;
    uint16_t vibration_level;
    uint8_t maintenance_needed;    // 0-100%
    uint32_t last_maintenance;     // timestamp
    uint16_t wear_factor;          // 0-1000
} Motor_Health_t;

Motor_Health_t motor_health[8]; // M1-M7

void Check_Maintenance_Needs(void) {
    for(uint8_t i = 1; i <= 7; i++) {
        Motor_Health_t* health = &motor_health[i];
        Motor_Diagnostics_t* stats = &motor_stats[i];

        // Сброс уровня необходимости обслуживания
        health->maintenance_needed = 0;

        // Анализ износа по количеству циклов
        if(health->total_cycles > 100000) {
            health->maintenance_needed += 20;
        }

        // Анализ температурного режима
        if(health->temperature_max > 70) {
            health->maintenance_needed += 30;
        }

        // Анализ надежности
        if(stats->health_status < 80) {
            health->maintenance_needed += 25;
        }

        // Анализ времени работы
        if(health->total_runtime_hours > 1000) {
            health->maintenance_needed += 15;
        }

        // Анализ вибрации
        if(health->vibration_level > 500) {
            health->maintenance_needed += 10;
        }

        // Генерация предупреждений
        if(health->maintenance_needed > 80) {
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString("M%d: MAINTENANCE URGENT!", i);
            Generate_Maintenance_Alert(i);
        } else if(health->maintenance_needed > 50) {
            LCD_Send_Command(LCD_4_LINE_POS_0);
            LCD_SendString("M%d: Maintenance soon", i);
        }
    }
}

void Generate_Maintenance_Alert(uint8_t motor_id) {
    // Звуковой сигнал
    for(uint8_t i = 0; i < 5; i++) {
        BEEP_ON;
        Delay_mS(200);
        BEEP_OFF;
        Delay_mS(200);
    }

    // Детальная информация
    Motor_Health_t* health = &motor_health[motor_id];

    LCD_Send_Command(LCD_1_LINE_POS_0);
    LCD_SendString("=== MAINTENANCE M%d ===", motor_id);

    LCD_Send_Command(LCD_2_LINE_POS_0);
    LCD_SendString("Need: %d%%, Hours: %d",
                  health->maintenance_needed,
                  health->total_runtime_hours);

    LCD_Send_Command(LCD_3_LINE_POS_0);
    LCD_SendString("Cycles: %d, MaxT: %d°C",
                  health->total_cycles,
                  health->temperature_max);

    LCD_Send_Command(LCD_4_LINE_POS_0);
    LCD_SendString("Press any key...");

    // Ждем нажатия любой кнопки
    while(SW1 && SW2 && SW3 && SW4 && SW5 && SW6 && SW7) {
        Delay_mS(10);
    }
}
```

---

## 📊 КОНТРОЛЬНЫЕ ТОЧКИ И ВРЕМЕННЫЕ РАМКИ

### **ПРОГРАММНЫЕ КОНТРОЛЬНЫЕ ТОЧКИ** (Чеклист)

#### **Через 1 неделю:**
- [ ] **M6:** Исправлена логика остановки (проверка D3 вместо D4)
- [ ] **M6:** Добавлены таймауты защиты (максимум 1000 шагов)
- [ ] **M2:** Увеличен ток драйвера на 20-30%
- [ ] **M2:** Закреплен кронштейн крепления мотора
- [ ] **Все моторы:** Добавлена базовая диагностика

#### **Ожидаемый результат**
- [ ] **M6:** Корректно останавливается при обнаружении мины
- [ ] **M2:** Надежно держит позицию ствола
- [ ] **M3:** Работает на максимальной безопасной скорости
- [ ] **Сценарий READY:** Выполняется без ошибок за < 20 секунд
- [ ] **Система:** Добавлена полная диагностическая система
- [ ] **Все моторы:** Имеют защиту от зависания

#### **Через 1 месяц:**
- [ ] **Автокалибровка:** Реализована для всех моторов
- [ ] **Предиктивное обслуживание:** Система мониторинга износа
- [ ] **Статистика:** Детальные отчеты о работе моторов
- [ ] **Оптимизация:** Скорость READY < 15 секунд
- [ ] **Надежность:** 99%+ успешных выполнений команд

### **МЕХАНИЧЕСКИЕ КОНТРОЛЬНЫЕ ТОЧКИ**

#### **Через 1 неделю:**
- [ ] **M2:** Установлена пружина стабилизации
- [ ] **M3:** Отрегулировано натяжение цепи
- [ ] **M4:** Перенесены датчики в безопасное место
- [ ] **M5:** Переставлен упор в правильное положение
- [ ] **M6:** Проверен и откалиброван датчик D3

#### **Ожидаемый результат:**
- [ ] **Все моторы:** Установлены радиаторы охлаждения
- [ ] **M3:** Улучшен механизм сцепления каретки
- [ ] **M2:** Установлен электромагнитный тормоз (опционально)
- [ ] **Система:** Улучшена вентиляция корпуса
- [ ] **Все механизмы:** Проведена полная смазка

---

## ⚠️ МЕРЫ БЕЗОПАСНОСТИ

### **ПЕРЕД ЛЮБЫМИ РАБОТАМИ:**
1. **Отключить питание системы** - главный выключатель в положение "OFF"
2. **Убедиться что все моторы остановлены** - визуальная проверка
3. **Использовать антистатические меры** - браслет, коврик
4. **Проверить отсутствие мин в барабане** - безопасность персонала

### **ПРИ РАБОТЕ С ДРАЙВЕРАМИ:**
1. **Не превышать максимальный ток** - контроль по документации
2. **Контролировать температуру** - тепловизор или термометр
3. **Иметь запасные драйверы** - на случай выхода из строя
4. **Использовать изолированный инструмент** - защита от КЗ

### **ПРИ ТЕСТИРОВАНИИ:**
1. **Начинать с минимальных скоростей** - постепенное увеличение
2. **Постоянно контролировать состояние** - визуально и по приборам
3. **Иметь кнопку аварийной остановки** - в пределах досягаемости
4. **Работать в паре** - один оператор, один наблюдатель

### **КРИТИЧЕСКИЕ ПРЕДУПРЕЖДЕНИЯ:**
⚠️ **M6 - ОСОБАЯ ОСТОРОЖНОСТЬ:** Может вращаться бесконечно при программной ошибке
⚠️ **M2 - РИСК ПАДЕНИЯ:** Дуло может упасть при отключении питания
⚠️ **Высокое напряжение:** 24V в системе - риск поражения током
⚠️ **Движущиеся части:** Риск травмирования при работающих моторах

---

## 📋 ЗАКЛЮЧЕНИЕ

### **ТЕКУЩИЙ СТАТУС СИСТЕМЫ:**
- **Функциональность:** 70% (работает с ограничениями)
- **Надежность:** 40% (критические ошибки M6) <span style="color:red"> (Для системы контроля это критические проблемы несомненно, но нужно учитывать то, что при этом много проверямых ей факторов на данном этапе сделаны намерянно для удобства разработки, как например дублирование функций это критическая проблема, когда этим функционалом в процессе разработки пользуются для облегчения процесса. В конечном результате этих проблем не будет)</span>
- **Производительность:** 65% (средне)
- **Безопасность:** 60% (есть риски)

### **ОЖИДАЕМЫЙ РЕЗУЛЬТАТ ПОСЛЕ ВЫПОЛНЕНИЯ ПЛАНА:**
- **Функциональность:** 95% (полная функциональность)
- **Надежность:** 90% (устранены критические ошибки)
- **Производительность:** 85% (ускорение в 3-4 раза)
- **Безопасность:** 95% (все риски минимизированы)

### **КЛЮЧЕВЫЕ ДОСТИЖЕНИЯ:**
1. **Устранение критической ошибки M6** - предотвращение повреждения механизма
2. **Ускорение работы системы** - сокращение времени READY с 60с до 15с
3. **Повышение надежности** - добавление систем диагностики и защиты
4. **Улучшение обслуживания** - предиктивная диагностика и автокалибровка

---

**Документ подготовлен:** Инжинерной системой контроля кода и событий  CORDON-82 на базе ИИ Augment 
**Версия:** 2.0
**Дата:** 10/06/2025
**Статус:** ✅ **ГОТОВ К ВЫПОЛНЕНИЮ**

*Все работы должны выполняться квалифицированным персоналом с соблюдением мер безопасности*