#ifndef MOTOR_UNIFIED_CONFIG_H
#define MOTOR_UNIFIED_CONFIG_H

#include <stdint.h>

// =================================================================
// УНИФИЦИРОВАННАЯ СИСТЕМА КОНФИГУРАЦИИ МОТОРОВ CORDON-82
// ВСЕ ЗАДЕРЖКИ В МИКРОСЕКУНДАХ ДЛЯ ЕДИНООБРАЗИЯ
// =================================================================

// Максимальное количество моторов
#define MAX_MOTORS 8

// Типы направлений (унифицированные)
#define MOTOR_FORWARD  1
#define MOTOR_BACKWARD 0

// Статусы моторов
#define MOTOR_STATUS_OK       0
#define MOTOR_STATUS_TIMEOUT  1
#define MOTOR_STATUS_ERROR    2
#define MOTOR_STATUS_DISABLED 3

// =================================================================
// СТРУКТУРА КОНФИГУРАЦИИ МОТОРА
// =================================================================
typedef struct {
    // Временные параметры (ВСЕ В МИКРОСЕКУНДАХ!)
    uint32_t step_delay_us;     // Задержка импульса (мкс)
    uint32_t pulse_width_us;    // Ширина импульса (мкс)
    uint32_t extra_delay_us;    // Дополнительная задержка (для M2)
    
    // Характеристики производительности
    uint32_t max_speed_hz;      // Максимальная частота (Гц)
    uint16_t max_steps;         // Максимум шагов для таймаута
    uint16_t progress_step;     // Шаг для индикации прогресса
    
    // Настройки мотора
    uint8_t enabled;            // Включен ли мотор (0/1)
    uint8_t motor_type;         // Тип: 0=шаговый, 1=DC
    uint8_t has_encoder;        // Есть ли энкодер (0/1)
    uint8_t reverse_direction;  // Инвертировать направление (0/1)
    
    // Идентификация
    char name[8];               // Имя мотора для отладки
    char description[32];       // Описание функции мотора
} motor_config_t;

// =================================================================
// СТРУКТУРА СТАТИСТИКИ МОТОРА
// =================================================================
typedef struct {
    uint32_t total_steps;       // Общее количество шагов
    uint32_t successful_moves;  // Успешные перемещения
    uint32_t timeouts;          // Количество таймаутов
    uint32_t errors;            // Количество ошибок
    uint32_t last_move_time;    // Время последнего движения (мс)
    uint16_t last_move_steps;   // Шагов в последнем движении
    uint8_t health_status;      // Состояние здоровья (0-100%)
    uint8_t current_position;   // Текущая позиция (номер датчика)
    uint8_t last_direction;     // Последнее направление
    uint8_t temperature;        // Температура драйвера (если доступно)
} motor_stats_t;

// =================================================================
// ГЛОБАЛЬНЫЕ МАССИВЫ КОНФИГУРАЦИИ
// =================================================================

// Конфигурация всех моторов (индекс = номер мотора)
extern motor_config_t motors[MAX_MOTORS];

// Статистика всех моторов
extern motor_stats_t motor_stats[MAX_MOTORS];

// =================================================================
// УНИФИЦИРОВАННАЯ КОНФИГУРАЦИЯ МОТОРОВ CORDON-82
// =================================================================

// M0 - НЕ ИСПОЛЬЗУЕТСЯ
#define M0_STEP_DELAY_US    0
#define M0_PULSE_WIDTH_US   0
#define M0_MAX_SPEED_HZ     0
#define M0_MAX_STEPS        0
#define M0_ENABLED          0

// M1 - МОТОР ГОРИЗОНТАЛЬНОЙ НАВОДКИ (NEMA23 + редуктор 1:40)
// УСКОРЕННЫЕ НАСТРОЙКИ: 1000 мкс = 500 Гц
#define M1_STEP_DELAY_US    1000    // 1 мс = 1000 мкс
#define M1_PULSE_WIDTH_US   1000    // 1 мс = 1000 мкс  
#define M1_EXTRA_DELAY_US   0       // Нет дополнительной задержки
#define M1_MAX_SPEED_HZ     500     // Максимальная частота
#define M1_MAX_STEPS        3000    // Таймаут защиты
#define M1_PROGRESS_STEP    300     // Индикация каждые 300 шагов
#define M1_ENABLED          1       // Включен
#define M1_MOTOR_TYPE       0       // Шаговый
#define M1_HAS_ENCODER      1       // Есть энкодер
#define M1_REVERSE_DIR      0       // Не инвертировать

// M2 - МОТОР ВЕРТИКАЛЬНОЙ НАВОДКИ (NEMA34 + редуктор 1:10)
// МОЩНЫЕ НАСТРОЙКИ: 10000 мкс = 50 Гц (держит позицию!)
#define M2_STEP_DELAY_US    10000   // 10 мс = 10000 мкс
#define M2_PULSE_WIDTH_US   10000   // 10 мс = 10000 мкс
#define M2_EXTRA_DELAY_US   10000   // 10 мс дополнительно для CCW
#define M2_MAX_SPEED_HZ     50      // Мощная частота
#define M2_MAX_STEPS        2000    // Таймаут защиты
#define M2_PROGRESS_STEP    200     // Индикация каждые 200 шагов
#define M2_ENABLED          1       // Включен
#define M2_MOTOR_TYPE       0       // Шаговый
#define M2_HAS_ENCODER      1       // Есть энкодер
#define M2_REVERSE_DIR      0       // Не инвертировать

// M3 - МОТОР КАРЕТКИ (цепная передача)
// УЛЬТРА БЫСТРЫЕ НАСТРОЙКИ: 800 мкс = 1250 Гц
#define M3_STEP_DELAY_US    800     // 800 мкс
#define M3_PULSE_WIDTH_US   800     // 800 мкс
#define M3_EXTRA_DELAY_US   0       // Нет дополнительной задержки
#define M3_MAX_SPEED_HZ     1250    // Высокая частота
#define M3_MAX_STEPS        2000    // Таймаут защиты
#define M3_PROGRESS_STEP    200     // Индикация каждые 200 шагов
#define M3_ENABLED          1       // Включен
#define M3_MOTOR_TYPE       0       // Шаговый
#define M3_HAS_ENCODER      0       // Нет энкодера
#define M3_REVERSE_DIR      0       // Не инвертировать

// M4 - МОТОР ПРОДОЛЬНОГО ПЕРЕМЕЩЕНИЯ (ременная передача)
// УЛЬТРА БЫСТРЫЕ НАСТРОЙКИ: 500 мкс = 2000 Гц
#define M4_STEP_DELAY_US    500     // 500 мкс
#define M4_PULSE_WIDTH_US   500     // 500 мкс
#define M4_EXTRA_DELAY_US   0       // Нет дополнительной задержки
#define M4_MAX_SPEED_HZ     2000    // Очень высокая частота
#define M4_MAX_STEPS        1500    // Таймаут защиты
#define M4_PROGRESS_STEP    150     // Индикация каждые 150 шагов
#define M4_ENABLED          1       // Включен
#define M4_MOTOR_TYPE       0       // Шаговый
#define M4_HAS_ENCODER      0       // Нет энкодера
#define M4_REVERSE_DIR      0       // Не инвертировать

// M5 - МОТОР ВРАЩЕНИЯ МЕХАНИЗМА ЗАГРУЗКИ (прямая передача)
// УЛЬТРА БЫСТРЫЕ НАСТРОЙКИ: 600 мкс = 1667 Гц
#define M5_STEP_DELAY_US    600     // 600 мкс
#define M5_PULSE_WIDTH_US   600     // 600 мкс
#define M5_EXTRA_DELAY_US   0       // Нет дополнительной задержки
#define M5_MAX_SPEED_HZ     1667    // Высокая частота
#define M5_MAX_STEPS        1000    // Таймаут защиты
#define M5_PROGRESS_STEP    100     // Индикация каждые 100 шагов
#define M5_ENABLED          1       // Включен
#define M5_MOTOR_TYPE       0       // Шаговый
#define M5_HAS_ENCODER      0       // Нет энкодера
#define M5_REVERSE_DIR      0       // Не инвертировать

// M6 - МОТОР ВРАЩЕНИЯ БАРАБАНА (ременная передача 1:3)
// НАСТРОЙКИ ИЗ main.c: 1000 мкс = 500 Гц
#define M6_STEP_DELAY_US    1000    // 1 мс = 1000 мкс
#define M6_PULSE_WIDTH_US   1000    // 1 мс = 1000 мкс
#define M6_EXTRA_DELAY_US   0       // Нет дополнительной задержки
#define M6_MAX_SPEED_HZ     500     // Средняя частота
#define M6_MAX_STEPS        1000    // Таймаут защиты
#define M6_PROGRESS_STEP    100     // Индикация каждые 100 шагов
#define M6_ENABLED          1       // Включен
#define M6_MOTOR_TYPE       0       // Шаговый
#define M6_HAS_ENCODER      0       // Нет энкодера
#define M6_REVERSE_DIR      0       // Не инвертировать

// M7 - DC МОТОР ЗАХВАТА (12V 2A + редуктор 1:30)
// ОСОБЫЕ НАСТРОЙКИ ДЛЯ DC МОТОРА
#define M7_STEP_DELAY_US    0       // Не применимо для DC
#define M7_PULSE_WIDTH_US   0       // Не применимо для DC
#define M7_EXTRA_DELAY_US   0       // Не применимо для DC
#define M7_MAX_SPEED_HZ     0       // Не применимо для DC
#define M7_MAX_STEPS        0       // Не применимо для DC
#define M7_PROGRESS_STEP    0       // Не применимо для DC
#define M7_ENABLED          1       // Включен
#define M7_MOTOR_TYPE       1       // DC мотор
#define M7_HAS_ENCODER      0       // Нет энкодера
#define M7_REVERSE_DIR      0       // Не инвертировать
#define M7_TIMEOUT_MS       10000   // Таймаут 10 секунд
#define M7_CHECK_DELAY_MS   250     // Проверка каждые 250 мс

// =================================================================
// ФУНКЦИИ УПРАВЛЕНИЯ УНИФИЦИРОВАННОЙ СИСТЕМОЙ
// =================================================================

// Инициализация системы
void Motor_System_Init(void);

// Универсальная функция управления мотором
uint8_t Rotate_Motor_Universal(uint8_t motor_id, uint8_t direction, uint8_t target_sensor);

// Обновление статистики мотора
void Update_Motor_Stats(uint8_t motor_id, uint8_t success, uint16_t steps);

// Получение конфигурации мотора
motor_config_t* Get_Motor_Config(uint8_t motor_id);

// Получение статистики мотора
motor_stats_t* Get_Motor_Stats(uint8_t motor_id);

// Установка новых параметров мотора
void Set_Motor_Speed(uint8_t motor_id, uint32_t step_delay_us);
void Set_Motor_Max_Steps(uint8_t motor_id, uint16_t max_steps);
void Enable_Motor_Config(uint8_t motor_id, uint8_t enabled);

// Диагностика и отображение
void Show_Motor_Config(uint8_t motor_id);
void Show_All_Motors_Config(void);
void Show_Motor_Stats(uint8_t motor_id);
void Show_All_Motors_Stats(void);

// Автоматическая оптимизация
void Auto_Optimize_Motor_Speed(uint8_t motor_id);
void Test_Motor_Speed_Range(uint8_t motor_id, uint32_t min_delay, uint32_t max_delay);

// Сохранение/загрузка конфигурации
void Save_Motor_Config_To_Flash(void);
void Load_Motor_Config_From_Flash(void);
void Reset_Motor_Config_To_Default(void);

// Вспомогательные функции
uint8_t Check_Target_Sensor(uint8_t sensor_id);

#endif // MOTOR_UNIFIED_CONFIG_H
