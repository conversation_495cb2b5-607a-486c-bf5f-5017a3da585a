#ifndef USERFUNCTION_H
#define USERFUNCTION_H

#include "stdint.h"
#include "stm32f10x.h"
#include "lcd.h"

							/*For AD Convertion*/
//#define OVERCURRENT 	0x424 //22A		
#define OVERCURRENT 	22							
//#define NORMALCURRENT 	0x2D3 //15A	
#define NORMALCURRENT 15							
		


extern uint16_t VREF;           //Reference voltage for ADC in mV
extern const float VREF_4095;   //VREF/4095	(3400/4095)

extern uint16_t ADC_Result_CH0; //AD Convertion of CH0
extern uint16_t ADC_10Results_CH0[];//To store 10 ADC results

extern uint16_t ADC_Result_CH1; //AD Convertion of CH1
extern uint16_t ADC_10Results_CH1;//To store 10 ADC results

extern float ADC_CH0_mV;       //ADC Result in mV of CH0 - Current Sensor
extern uint16_t ADC_Int_CH0;         //Integer part of ADC Result in mV of CH0 - Current Sensor
extern uint16_t ADC_Frac_CH0;        //Fraction part of ADC Result in mV of CH0 - Current Sensor
extern uint8_t ADC_Char_CH0[];    //ADC Result in Chars of CH0 - Current Sensor
extern uint16_t Current;          //To store MSB and LSB of Current value
extern uint8_t CurrentCharBuffer[];//To store chars of MSB and LSB of Current value

extern float ADC_CH1_mV;       //ADC Result in mV of CH1 - Voltage Sensor
extern uint16_t ADC_Int_CH1;      //Integer part of ADC Result in mV of CH1 - Voltage Sensor
extern uint16_t ADC_Frac_CH1;     //Fraction part of ADC Result in mV of CH1 - Voltage Sensor
extern uint8_t ADC_Char_CH1[];  //ADC Result in Chars of CH1 - Voltage Sensor
extern uint16_t Voltage;          //To store MSB and LSB of Voltage value
extern uint8_t VoltageCharBuffer[];//To store chars of MSB and LSB of Voltage value

extern float OUT_Current;
extern uint16_t OUT_Current_Int;
extern uint16_t OUT_Current_Frac; 

extern float OUT_Voltage;
extern uint16_t OUT_Voltage_Int;
extern uint16_t OUT_Voltage_Frac;

extern void ADC1_Config(void);
extern uint16_t ADC1_Connvert(uint8_t );
extern void GetAdc_CH0CH1(void);
extern void AdcToLcd(void);
extern void Current_To_LCD(uint8_t *buf, uint8_t *lcd_buf);

void Signal_3p1D(void);//...-
void Signal_1D1p1D(void);//-.-							 
void Signal_Beep(uint8_t);
void Signal_OK(void);
void Signal_NO(void);

void Send_To_Main(uint8_t* string, int len);//Sends len symbols to UART1, 

#define CW 0 //rotate direction for M3 - M7
#define CCW 1

#define M3_Forward 1
#define M3_Back 2

#define M4_Forward 1
#define M4_Back 2

#define M5_Forward 1
#define M5_Back 2

#define M6_Forward 1 //CW
#define M6_Back 2 //CCW

#define M7_Forward 1
#define M7_Back 2

// =================================================================
// ИСХОДНЫЕ ПОЗИЦИИ МОТОРОВ (HOME POSITIONS)
// =================================================================
// M1 - Горизонтальная наводка: исходная позиция D14 (0 градусов)
// M2 - Вертикальная наводка: исходная позиция D13 (горизонт)
// M3 - Каретка: исходная позиция D2 (верхнее положение, готов к загрузке)
// M4 - Продольное перемещение: исходная позиция D7 (втянуто)
// M5 - Вращение механизма загрузки: исходная позиция D5 (готов к работе)
// M6 - Вращение барабана: исходная позиция D4 (мина в активной секции)
// M7 - DC мотор захвата: исходная позиция D10 (захват разжат)

#define M1_HOME_POSITION D14  // Горизонтальная наводка - 0 градусов
#define M2_HOME_POSITION D13  // Вертикальная наводка - горизонт
#define M3_HOME_POSITION D2   // Каретка - верхнее положение
#define M4_HOME_POSITION D7   // Продольное перемещение - втянуто
#define M5_HOME_POSITION D5   // Механизм загрузки - готов к работе
#define M6_HOME_POSITION D4   // Барабан - мина в активной секции
#define M7_HOME_POSITION D10  // Захват - разжат

extern uint16_t Encoders_Angele;
extern uint16_t Encoder1_Position;
extern uint16_t Encoder2_Position;

extern uint16_t M1_Angele;
extern uint16_t M2_Angele;
extern uint16_t Rotate_Angele;

// =================================================================
// НАСТРОЙКИ ВСЕХ МОТОРОВ - ОБЪЯВЛЕНИЯ ВНЕШНИХ ПЕРЕМЕННЫХ
// =================================================================

// M1 - МОТОР ГОРИЗОНТАЛЬНОЙ НАВОДКИ
extern uint16_t M1_StepDelay;      // мс - задержка импульса
extern uint16_t M1_PulseWidth;     // мс - ширина импульса
extern uint16_t M1_MaxSpeed;       // Гц - максимальная частота

// M2 - МОТОР ВЕРТИКАЛЬНОЙ НАВОДКИ
extern uint16_t M2_StepDelay_CW;    // мс - задержка для CW
extern uint16_t M2_StepDelay_CCW;   // мс - первая задержка для CCW
extern uint16_t M2_PulseWidth_CCW;  // мс - вторая задержка для CCW
extern uint16_t M2_ExtraDelay_CCW;  // мс - третья задержка для CCW
extern uint16_t M2_MaxSpeed;        // Гц - максимальная частота

// M3 - МОТОР КАРЕТКИ
extern uint16_t M3_StepDelay;       // мс - задержка импульса
extern uint16_t M3_PulseWidth;      // мс - ширина импульса
extern uint16_t M3_MaxSpeed;        // Гц - максимальная частота
extern uint16_t M3_StepDelay_uS;    // мкс - УЛЬТРА БЫСТРЫЕ настройки
extern uint16_t M3_PulseWidth_uS;   // мкс - УЛЬТРА БЫСТРЫЕ настройки

// M4 - МОТОР ПРОДОЛЬНОГО ПЕРЕМЕЩЕНИЯ
extern uint16_t M4_StepDelay;       // мс - задержка импульса
extern uint16_t M4_PulseWidth;      // мс - ширина импульса
extern uint16_t M4_MaxSpeed;        // Гц - максимальная частота
extern uint16_t M4_StepDelay_uS;    // мкс - УЛЬТРА БЫСТРЫЕ настройки
extern uint16_t M4_PulseWidth_uS;   // мкс - УЛЬТРА БЫСТРЫЕ настройки

// M5 - МОТОР ВРАЩЕНИЯ МЕХАНИЗМА ЗАГРУЗКИ
extern uint16_t M5_StepDelay;       // мс - задержка импульса
extern uint16_t M5_PulseWidth;      // мс - ширина импульса
extern uint16_t M5_MaxSpeed;        // Гц - максимальная частота
extern uint16_t M5_StepDelay_uS;    // мкс - УЛЬТРА БЫСТРЫЕ настройки
extern uint16_t M5_PulseWidth_uS;   // мкс - УЛЬТРА БЫСТРЫЕ настройки

// M6 - МОТОР ВРАЩЕНИЯ БАРАБАНА
extern uint16_t M6_StepDelay;       // мс - задержка импульса
extern uint16_t M6_PulseWidth;      // мс - ширина импульса
extern uint16_t M6_MaxSpeed;        // Гц - максимальная частота

// M7 - DC МОТОР ЗАХВАТА
extern uint16_t M7_Timeout;        // мс - таймаут операции
extern uint16_t M7_CheckDelay;     // мс - интервал проверки датчиков

// =================================================================

//For Errors:
extern uint8_t M7_Error;
extern uint8_t SensorPositionError;
//uint8_t 

void GetEncoder_1_Angele(void);
void GetEncoder_2_Angele(void);

void RotateM1_D14_Position(void);
void RotateM2_D13_Position(void);

void Rotate_M1_CW(uint16_t);
void Rotate_M2_CW(uint16_t);

void Rotate_M1_CCW(uint16_t);
void Rotate_M2_CCW(uint16_t);

void Rotate_M3(uint8_t);//Forward or Backward

void Rotate_M4(uint8_t);

void Rotate_M5(uint8_t);

void Rotate_M6(uint8_t);

void Rotate_M6_Step(uint8_t);

void Rotate_M6_Stage(void);

void Rotate_M6_Max_Power(uint8_t direction, uint16_t steps);

void Rotate_M7(uint8_t);

void LoadUp(void);

void Ready_Command(void);

void Return_All_Motors_Home(void);

void Test_All_Motors_Max_Speed(void);

void Test_M1_Simple(void);

void Play_Note(uint16_t frequency, uint16_t duration_ms);

void Play_Piano_Melody(void);

void Show_About_Page(void);

// =================================================================
// ВСТРОЕННАЯ КОНФИГУРАЦИЯ МОТОРОВ (БЕЗ SD КАРТЫ)
// =================================================================

typedef struct {
    uint16_t step_delay;     // Задержка между шагами (мкс)
    uint16_t pulse_width;    // Длительность импульса (мкс)
    uint32_t max_speed;      // Максимальная скорость (шагов/сек)
    uint8_t  acceleration;   // Разгон (0=нет, 1=есть)
    uint8_t  direction;      // Направление по умолчанию
    uint8_t  enabled;        // Включен ли мотор (0=нет, 1=да)
} Motor_Config_t;

typedef struct {
    Motor_Config_t motors[7];  // M1-M7
    uint8_t config_version;
    char description[64];
} System_Config_t;

// Глобальная конфигурация системы
extern const System_Config_t EMBEDDED_CONFIG;

// Переменные таймера READY команды
extern uint32_t g_timer_start_ms;
extern uint32_t g_timer_current_ms;
extern uint8_t g_timer_running;

// Функции работы с конфигурацией
void Load_Embedded_Config(void);
void Show_Current_Config(void);

// Функции таймера для READY команды
void Timer_Start(void);
void Timer_Stop_And_Show(const char* checkpoint_name);
void Timer_Reset(void);
uint32_t Timer_Get_Elapsed_MS(void);

void Rotate_M6_Step_Safe(uint8_t direction);
void Rotate_M3_Adaptive(uint8_t direction);
void M2_Hold_Position_Active(void);
void M2_Hold_Position_Inactive(void);
void M2_Hold_Position(void);
void Update_Motor_Stats(uint8_t motor_num, uint8_t success, uint32_t operation_time);
void Show_All_Motors_Status(void);
uint8_t Test_Motor_Stability(uint8_t motor_num, uint16_t delay_us, uint16_t test_steps);
void Auto_Calibrate_All_Motors(void);
uint32_t Get_System_MS(void);
void Generate_Maintenance_Alert(uint8_t motor_id);

#endif /* USERFUNCTION_H */
