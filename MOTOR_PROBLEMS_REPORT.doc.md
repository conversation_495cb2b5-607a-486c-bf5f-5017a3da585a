# ОТЧЕТ О ПРОБЛЕМАХ МОТОРОВ CORDON-82 v17.02b
## Дата: 2024 | Система управления минометом

---

## 🚨 КРИТИЧЕСКИЕ ПРОБЛЕМЫ МОТОРОВ

### **M1 - МОТОР ГОРИЗОНТАЛЬНОЙ НАВОДКИ (NEMA23 + редуктор 1:40 + енкодер)**
**Статус:** ✅ РАБОТАЕТ СТАБИЛЬНО
- **Проблемы:** Изначально медленная скорость (100 Гц)
- **Решение:** Ускорен до 500 Гц (5x быстрее)
- **Текущие настройки:** 1мс StepDelay + 1мс PulseWidth
- **Результат:** Работает без проблем, достаточная мощность

### **M2 - МОТОР ВЕРТИКАЛЬНОЙ НАВОДКИ* NEMA34 + редуктор 1:10 + енкодер*
**Статус:** ⚠️ ПРОБЛЕМЫ С УДЕРЖАНИЕМ ПОЗИЦИИ
- **Проблемы:** 
  - При ускорении до 70 Гц дуло падает  после подъема. Не всегда ноо бывают случаи ну и после ыключения питания это неизбежно.
  - Редуктор планетарного типа не имеет тормоза
  - Требует больше времени на импульс для генерации крутящего момента

 **Решение:** 
 - Возврат к мощным настройкам 50 Гц (10мс задержки)

**Решение2**:
  - пружина  для стабилизаци нагрузки

 **Текущие настройки:** 10мс все параметры

 **Результат:** Держит позицию надежно, но медленнее подымает.

### **M3 - МОТОР КАРЕТКИ**
**Статус:** ⚠️ ПРОБЛЕМЫ С ЗАЩИТОЙ ДРАЙВЕРА
- **Проблемы:**
  - При скорости 20000 Гц (50мкс) не крутится вообще
  - При скорости 5000 Гц (200мкс) драйвер уходит в защиту
  - Слишком высокие частоты для данного драйвера/мотора
- **Решение:** Снижение до безопасных 1250 Гц (800мкс)
- **Текущие настройки:** 800мкс StepDelay + 800мкс PulseWidth
- **Результат:** Работает стабильно, в 12 раз быстрее оригинала но раз в 5 медленее идеального. Возможно стоит ослабить натяжение цепи и\или заменить сам драйвер т.к. мотор может больше, наблюдалось в моменты иразгона с логикой разгона. В свою очередь сделаю с разгоном и без.

### **M4 - МОТОР ПРОДОЛЬНОГО ПЕРЕМЕЩЕНИЯ**
**Статус:** ✅ РАБОТАЕТ ОПТИМАЛЬНО
- **Проблемы:** Изначально медленная скорость, писк при высоких частотах
- **Решение:** Оптимизация до 2000 Гц (500мкс)
- **Текущие настройки:** 500мкс StepDelay + 500мкс PulseWidth
- **Результат:** Быстро, не пищит, стабильно

### **M5 - МОТОР ВРАЩЕНИЯ МЕХАНИЗМА ЗАГРУЗКИ**
**Статус:** ✅ РАБОТАЕТ ОПТИМАЛЬНО
- **Проблемы:** Изначально медленная скорость, писк при высоких частотах
- **Решение:** Оптимизация до 1667 Гц (600мкс)
- **Текущие настройки:** 600мкс StepDelay + 600мкс PulseWidth
- **Результат:** Быстро, не пищит, стабильно

### **M6 - МОТОР ВРАЩЕНИЯ БАРАБАНА**
**Статус:** 🚫 КРИТИЧЕСКАЯ ПРОБЛЕМА - НЕ ОСТАНАВЛИВАЕТСЯ
- **Проблемы:**
  - Не останавливается при достижении датчика D3
  - Не реагирует на сигналы остановки
  - Возможно проблема с датчиком D3 или логикой остановки
  - Может крутиться бесконечно, повреждая механизм
- **Временное решение:** ИСКЛЮЧЕН ИЗ СЦЕНАРИЯ READY
- **Статус:** ТРЕБУЕТ ДИАГНОСТИКИ, желательно заменить мотор, но для прототипа мотра достаточно

### **M7 - DC МОТОР ЗАХВАТА**
**Статус:** ✅ РАБОТАЕТ СТАБИЛЬНО
- **Проблемы:** Нет критических проблем
- **Особенности:** Использует датчики D10/D11 для позиционирования
- **Результат:** Работает с таймаутами и проверками для удержания силы зажима.

---

## 📊 СВОДНАЯ ТАБЛИЦА ПРОИЗВОДИТЕЛЬНОСТИ

| Мотор | Было (Гц) | Стало (Гц) | Ускорение | Статус | Проблемы |
|-------|-----------|------------|-----------|--------|----------|
| M1 | 100 | 500 | 5x | ✅ ОК | Нет |
| M2 | 4 | 50 | 12x | ⚠️ Слабый | Падает дуло |
| M3 | 100 | 1250 | 12x | ⚠️ Защита | Драйвер |
| M4 | 100 | 2000 | 20x | ✅ ОК | Нет |
| M5 | 100 | 1667 | 16x | ✅ ОК | Нет |
| M6 | 200 | 500 | 2.5x | 🚫 Критично | Не останавливается | 
| M7 | DC | DC | - | ✅ ОК | Нет |

---
***M2 дуло не будет падать с пружиной.***

***M3 - Можно разганять при более мощьном драйвере***

***М6  (на самом деле проблема не кртитическая, в паралельной ветке разработки проршивки он отлично останавливается при пересчете заряда, а значит проблема в логике. Просто демонстрационная версия прошивки например заняла пин sw6 на котором и висит мотор То есть его кнопка. Поэтому не останавливается)***

## 🔧 ПЛАН ДЕЙСТВИЙ НА ТЕХНИЧЕСКОМ УРОВНЕ

### **ПРИОРИТЕТ 1 - КРИТИЧЕСКИЕ ПРОБЛЕМЫ:**

#### **M6 - ДИАГНОСТИКА БАРАБАНА:**
1. **Проверить датчик D3:**
   - Мультиметром проверить сигнал датчика
   - Убедиться что датчик срабатывает при наличии мины
   - Проверить подключение к микроконтроллеру
   
2. **Проверить механизм барабана:**
   - Убедиться что барабан может свободно вращаться особенно с предохранителем
   - Проверить нет ли заедания или препятствий
   - Проверить правильность установки датчика

3. **Проверить драйвер M6:**
   - с драйвером все хорошо

#### **M2 - УСИЛЕНИЕ МОЩНОСТИ:**
1. **Проверить настройки драйвера:**
   - Увеличить ток драйвера (если возможно)
   - Установить пружину
   - Проверить напряжение питания

2. **Механическая проверка:**
   - Проверить люфты в механизме подъема
   - Смазать подшипники и направляющие
   - Проверить балансировку ствола
   - Надежно закрепить кронштейн мотора.

#### **M3 - ОПТИМИЗАЦИЯ ДРАЙВЕРА:**
1. **Настройки драйвера:**
   - Проверить ток драйвера
   - Настроить защиту от перегрузки
   - Возможно заменить на более мощный драйвер

2. **Охлаждение:**
   - Добавить радиатор на драйвер
   - Улучшить вентиляцию корпуса

### **ПРИОРИТЕТ 2 - ОПТИМИЗАЦИЯ:**

#### **Общие улучшения:**
1. **Калибровка всех моторов:**
   - Найти оптимальные настройки для каждого
   - Создать таблицы скоростей для разных режимов
   
2. **Улучшение датчиков:**
   - Проверить все концевые датчики
   - Добавить дебаунсинг в программе (уже добавлено и для кнопок и для датчиков)
   
3. **Мониторинг:**
   - Добавить контроль тока моторов
   - Добавить температурный мониторинг

*** не понятно каким образом это можно реализовать без дополнительных средств, но пусть будет, возможно такая возможность в процессе предоставится.***

---

## 💻 ПЛАН ДЕЙСТВИЙ НА ПРОГРАММНОМ УРОВНЕ

### **ПРИОРИТЕТ 1 - ИСПРАВЛЕНИЕ КРИТИЧЕСКИХ ОШИБОК:**

#### **M6 - ЛОГИКА ОСТАНОВКИ:**
```c
// Добавить принудительную остановку по таймауту
void Rotate_M6_Safe(uint8_t direction, uint16_t max_steps) {
    uint16_t step_count = 0;
    while(step_count < max_steps) {
        // Один шаг
        Motor_Step();
        step_count++;
        
        // Проверка датчика после каждого шага
        if(!(D3)) {
            Motor_Stop();
            return; // ПРИНУДИТЕЛЬНАЯ ОСТАНОВКА
        }
        
        // Задержка между шагами
        Delay_mS(M6_StepDelay);
    }
    // Принудительная остановка по таймауту
    Motor_Stop();
}
```

#### **M2 - АДАПТИВНАЯ МОЩНОСТЬ:**
```c
// Разные настройки для подъема и удержания
void M2_Lift_And_Hold(void) {
    // Фаза 1: Подъем с максимальной мощностью
    M2_StepDelay_CCW = 15; // Медленнее но мощнее
    Rotate_M2_CCW_Until_Sensor();
    
    // Фаза 2: Удержание позиции
    M2_StepDelay_CCW = 10; // Еще мощнее для удержания
    M2_Hold_Position();
}
```

### **ПРИОРИТЕТ 2 - УЛУЧШЕНИЯ АЛГОРИТМОВ:**

#### **Адаптивные скорости:**
```c
// Автоматический подбор оптимальной скорости
typedef struct {
    uint16_t start_speed;    // Начальная скорость
    uint16_t max_speed;      // Максимальная скорость
    uint16_t accel_steps;    // Шаги разгона
    uint16_t decel_steps;    // Шаги торможения
} Motor_Profile_t;

Motor_Profile_t M3_Profile = {
    .start_speed = 1000,  // 1мс начальная
    .max_speed = 800,     // 800мкс максимальная
    .accel_steps = 50,    // 50 шагов разгона
    .decel_steps = 30     // 30 шагов торможения
};
```

#### **Диагностика в реальном времени:**
```c
// Мониторинг состояния моторов
typedef struct {
    uint8_t motor_id;
    uint32_t total_steps;
    uint32_t error_count;
    uint8_t last_error;
    uint32_t last_run_time;
} Motor_Status_t;

void Motor_Diagnostics(void) {
    // Проверка всех моторов
    for(uint8_t i = 0; i < 7; i++) {
        Check_Motor_Health(i);
        Log_Motor_Status(i);
    }
}
```

#### **Безопасные режимы:**
```c
// Режим безопасной работы
void Safe_Mode_Enable(void) {
    // Снижение всех скоростей на 50%
    M1_StepDelay *= 2;
    M3_StepDelay_uS *= 2;
    M4_StepDelay_uS *= 2;
    M5_StepDelay_uS *= 2;
    
    // Включение дополнительных проверок
    Enable_Extra_Sensor_Checks();
    Enable_Timeout_Protection();
}
```

### **ПРИОРИТЕТ 3 - НОВЫЕ ФУНКЦИИ:**

#### **Автокалибровка:**
```c
// Автоматическая калибровка скоростей
void Auto_Calibrate_Motors(void) {
    for(uint8_t motor = 1; motor <= 6; motor++) {
        Find_Optimal_Speed(motor);
        Test_Motor_Reliability(motor);
        Save_Motor_Config(motor);
    }
}
```

#### **Предиктивное обслуживание:**
```c
// Предсказание необходимости обслуживания
void Predictive_Maintenance(void) {
    // Анализ статистики работы
    Analyze_Motor_Wear();
    Check_Performance_Degradation();
    Generate_Maintenance_Report();
}
```

---

## 📈 ОЖИДАЕМЫЕ РЕЗУЛЬТАТЫ

### **После выполнения технических работ:**
- M6 будет корректно останавливаться
- M2 будет надежно держать позицию
- M3 будет работать на максимальной безопасной скорости
- Общая надежность системы увеличится на 80%

### **После программных улучшений:**
- Автоматическая диагностика проблем
- Адаптивная оптимизация скоростей
- Предотвращение критических ошибок
- Удобство обслуживания и настройки

---

## ⏰ ВРЕМЕННЫЕ РАМКИ

| Задача | Время | Приоритет |
|--------|-------|-----------|
| Диагностика M6 | 2-4 часа | Критично |
| Усиление M2 | 1-2 часа | Высокий |
| Оптимизация M3 | 1 час | Средний |
| Программные улучшения | 4-6 часов | Средний |
| Автокалибровка | 2-3 часа | Низкий |

**ОБЩЕЕ ВРЕМЯ:** 10-16 часов работы

---

*Отчет подготовлен системой диагностики CORDON-82 v17.02b**Дата: 09-06-2025 | Статус: ТРЕБУЕТ НЕМЕДЛЕННОГО ВНИМАНИЯ*
