Component: Arm Compiler for Embedded 6.23 Tool: armlink [5f102400]

==============================================================================

Section Cross References

    main.o(.text.main) refers to rcc.o(.text.RCCInit) for RCCInit
    main.o(.text.main) refers to timers.o(.text.SetupTimers) for SetupTimers
    main.o(.text.main) refers to io_gpio.o(.text.SetupGpioIO) for SetupGpioIO
    main.o(.text.main) refers to userfunction.o(.text.ADC1_Config) for ADC1_Config
    main.o(.text.main) refers to i2c.o(.text.SetUp_I2C1) for SetUp_I2C1
    main.o(.text.main) refers to main.o(.text.Delay_mS) for Delay_mS
    main.o(.text.main) refers to main.o(.text.NVIC_EnableIRQ) for NVIC_EnableIRQ
    main.o(.text.main) refers to main.o(.text.NVIC_SetPriority) for NVIC_SetPriority
    main.o(.text.main) refers to main.o(.text.__enable_irq) for __enable_irq
    main.o(.text.main) refers to userfunction.o(.text.Signal_3p1D) for Signal_3p1D
    main.o(.text.main) refers to lcd.o(.text.LCD_Setup) for LCD_Setup
    main.o(.text.main) refers to main.o(.rodata.str1.1) for .L.str
    main.o(.text.main) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    main.o(.text.main) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    main.o(.text.main) refers to main.o(.bss.u8_Uart1_Cmd) for u8_Uart1_Cmd
    main.o(.text.main) refers to main.o(.text.SaveReceivedCommand) for SaveReceivedCommand
    main.o(.text.main) refers to main.o(.bss.u8_ReceivedCommand) for u8_ReceivedCommand
    main.o(.text.main) refers to json_parser.o(.text.JSON_Parse) for JSON_Parse
    main.o(.text.main) refers to json_parser.o(.text.JSON_ExecuteCommand) for JSON_ExecuteCommand
    main.o(.text.main) refers to main.o(.bss.u8_CmdNumber) for u8_CmdNumber
    main.o(.text.main) refers to userfunction.o(.text.Send_To_Main) for Send_To_Main
    main.o(.text.main) refers to userfunction.o(.text.Return_All_Motors_Home) for Return_All_Motors_Home
    main.o(.text.main) refers to userfunction.o(.text.GetEncoder_1_Angele) for GetEncoder_1_Angele
    main.o(.text.main) refers to main.o(.bss.M1_Angele) for M1_Angele
    main.o(.text.main) refers to userfunction.o(.text.GetEncoder_2_Angele) for GetEncoder_2_Angele
    main.o(.text.main) refers to main.o(.bss.M2_Angele) for M2_Angele
    main.o(.text.main) refers to main.o(.bss.Rotate_Angele) for Rotate_Angele
    main.o(.text.main) refers to userfunction.o(.text.Rotate_M1_CW) for Rotate_M1_CW
    main.o(.text.main) refers to userfunction.o(.text.Rotate_M1_CCW) for Rotate_M1_CCW
    main.o(.text.main) refers to userfunction.o(.text.Rotate_M2_CW) for Rotate_M2_CW
    main.o(.text.main) refers to userfunction.o(.text.Rotate_M2_CCW) for Rotate_M2_CCW
    main.o(.text.main) refers to main.o(.bss.projectile_number) for projectile_number
    main.o(.text.main) refers to userfunction.o(.text.Rotate_M6_Step) for Rotate_M6_Step
    main.o(.text.main) refers to userfunction.o(.text.Ready_Command) for Ready_Command
    main.o(.text.main) refers to main.o(.bss.SensorPositionError) for SensorPositionError
    main.o(.text.main) refers to userfunction.o(.text.Rotate_M3) for Rotate_M3
    main.o(.text.main) refers to userfunction.o(.text.Rotate_M4) for Rotate_M4
    main.o(.text.main) refers to userfunction.o(.text.Rotate_M5) for Rotate_M5
    main.o(.text.main) refers to userfunction.o(.text.Rotate_M6) for Rotate_M6
    main.o(.text.main) refers to userfunction.o(.text.Rotate_M7) for Rotate_M7
    main.o(.text.main) refers to main.o(.bss.M7_Error) for M7_Error
    main.o(.text.main) refers to userfunction.o(.text.Rotate_M6_Max_Power) for Rotate_M6_Max_Power
    main.o(.text.main) refers to userfunction.o(.text.Load_Embedded_Config) for Load_Embedded_Config
    main.o(.text.main) refers to userfunction.o(.text.Show_Current_Config) for Show_Current_Config
    main.o(.text.main) refers to userfunction.o(.text.Show_About_Page) for Show_About_Page
    main.o(.text.main) refers to userfunction.o(.text.Play_Piano_Melody) for Play_Piano_Melody
    main.o(.text.main) refers to userfunction.o(.text.Test_M1_Simple) for Test_M1_Simple
    main.o(.text.main) refers to userfunction.o(.text.Test_All_Motors_Max_Speed) for Test_All_Motors_Max_Speed
    main.o(.text.main) refers to main.o(.text.ClearCmdBuffer) for ClearCmdBuffer
    main.o(.text.main) refers to main.o(.bss.u8_CmdIndex_1) for u8_CmdIndex_1
    main.o(.text.main) refers to main.o(.data.M1_StepDelay) for M1_StepDelay
    main.o(.text.main) refers to main.o(.data.M1_PulseWidth) for M1_PulseWidth
    main.o(.text.main) refers to main.o(.bss.Encoders_Angele) for Encoders_Angele
    main.o(.text.main) refers to main.o(.data.M2_StepDelay_CW) for M2_StepDelay_CW
    main.o(.text.main) refers to main.o(.data.M3_StepDelay_uS) for M3_StepDelay_uS
    main.o(.text.main) refers to main.o(.text.Delay_uS) for Delay_uS
    main.o(.text.main) refers to main.o(.data.M3_PulseWidth_uS) for M3_PulseWidth_uS
    main.o(.text.main) refers to main.o(.data.M4_StepDelay_uS) for M4_StepDelay_uS
    main.o(.text.main) refers to main.o(.data.M4_PulseWidth_uS) for M4_PulseWidth_uS
    main.o(.text.main) refers to main.o(.data.M5_StepDelay_uS) for M5_StepDelay_uS
    main.o(.text.main) refers to main.o(.data.M5_PulseWidth_uS) for M5_PulseWidth_uS
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.Delay_mS) refers to main.o(.text.Delay_mS) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.NVIC_EnableIRQ) refers to main.o(.text.NVIC_EnableIRQ) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.NVIC_SetPriority) refers to main.o(.text.NVIC_SetPriority) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.__enable_irq) refers to main.o(.text.__enable_irq) for [Anonymous Symbol]
    main.o(.text.SaveReceivedCommand) refers to main.o(.bss.u8_CmdBuffer_1) for u8_CmdBuffer_1
    main.o(.text.SaveReceivedCommand) refers to main.o(.bss.u8_ReceivedCommand) for u8_ReceivedCommand
    main.o(.ARM.exidx.text.SaveReceivedCommand) refers to main.o(.text.SaveReceivedCommand) for [Anonymous Symbol]
    main.o(.text.ClearCmdBuffer) refers to main.o(.bss.u8_ReceivedCommand) for u8_ReceivedCommand
    main.o(.ARM.exidx.text.ClearCmdBuffer) refers to main.o(.text.ClearCmdBuffer) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.Delay_uS) refers to main.o(.text.Delay_uS) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.Int_To_Char) refers to main.o(.text.Int_To_Char) for [Anonymous Symbol]
    main.o(.text.USART1_IRQHandler) refers to main.o(.bss.u8_CmdIndex_1) for u8_CmdIndex_1
    main.o(.text.USART1_IRQHandler) refers to main.o(.bss.u8_CmdBuffer_1) for u8_CmdBuffer_1
    main.o(.text.USART1_IRQHandler) refers to main.o(.bss.u8_Uart1_Cmd) for u8_Uart1_Cmd
    main.o(.ARM.exidx.text.USART1_IRQHandler) refers to main.o(.text.USART1_IRQHandler) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.USART2_IRQHandler) refers to main.o(.text.USART2_IRQHandler) for [Anonymous Symbol]
    userfunction.o(.ARM.exidx.text.ADC1_Config) refers to userfunction.o(.text.ADC1_Config) for [Anonymous Symbol]
    userfunction.o(.ARM.exidx.text.ADC1_Connvert) refers to userfunction.o(.text.ADC1_Connvert) for [Anonymous Symbol]
    userfunction.o(.text.GetAdc_CH0CH1) refers to userfunction.o(.text.ADC1_Connvert) for ADC1_Connvert
    userfunction.o(.text.GetAdc_CH0CH1) refers to userfunction.o(.bss.ADC_Result_CH0) for ADC_Result_CH0
    userfunction.o(.text.GetAdc_CH0CH1) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.GetAdc_CH0CH1) refers to userfunction.o(.bss.ADC_Result_CH1) for ADC_Result_CH1
    userfunction.o(.ARM.exidx.text.GetAdc_CH0CH1) refers to userfunction.o(.text.GetAdc_CH0CH1) for [Anonymous Symbol]
    userfunction.o(.text.AdcToLcd) refers to userfunction.o(.bss.ADC_Result_CH0) for ADC_Result_CH0
    userfunction.o(.text.AdcToLcd) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    userfunction.o(.text.AdcToLcd) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    userfunction.o(.text.AdcToLcd) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    userfunction.o(.text.AdcToLcd) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    userfunction.o(.text.AdcToLcd) refers to userfunction.o(.bss.ADC_CH0_mV) for ADC_CH0_mV
    userfunction.o(.text.AdcToLcd) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmplt
    userfunction.o(.text.AdcToLcd) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    userfunction.o(.text.AdcToLcd) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    userfunction.o(.text.AdcToLcd) refers to userfunction.o(.bss.OUT_Current) for OUT_Current
    userfunction.o(.text.AdcToLcd) refers to userfunction.o(.bss.OUT_Current_Int) for OUT_Current_Int
    userfunction.o(.text.AdcToLcd) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    userfunction.o(.text.AdcToLcd) refers to userfunction.o(.bss.OUT_Current_Frac) for OUT_Current_Frac
    userfunction.o(.text.AdcToLcd) refers to userfunction.o(.bss.CurrentCharBuffer) for CurrentCharBuffer
    userfunction.o(.text.AdcToLcd) refers to main.o(.text.Int_To_Char) for Int_To_Char
    userfunction.o(.text.AdcToLcd) refers to lcd.o(.bss.LCD_Current) for LCD_Current
    userfunction.o(.text.AdcToLcd) refers to lcd.o(.text.Current_To_LCD) for Current_To_LCD
    userfunction.o(.text.AdcToLcd) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.AdcToLcd) refers to userfunction.o(.rodata.str1.1) for .L.str
    userfunction.o(.text.AdcToLcd) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.AdcToLcd) refers to userfunction.o(.bss.ADC_Result_CH1) for ADC_Result_CH1
    userfunction.o(.text.AdcToLcd) refers to userfunction.o(.bss.ADC_CH1_mV) for ADC_CH1_mV
    userfunction.o(.text.AdcToLcd) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    userfunction.o(.text.AdcToLcd) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    userfunction.o(.text.AdcToLcd) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    userfunction.o(.text.AdcToLcd) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    userfunction.o(.text.AdcToLcd) refers to userfunction.o(.bss.OUT_Voltage) for OUT_Voltage
    userfunction.o(.text.AdcToLcd) refers to userfunction.o(.bss.OUT_Voltage_Int) for OUT_Voltage_Int
    userfunction.o(.text.AdcToLcd) refers to userfunction.o(.bss.OUT_Voltage_Frac) for OUT_Voltage_Frac
    userfunction.o(.text.AdcToLcd) refers to userfunction.o(.bss.VoltageCharBuffer) for VoltageCharBuffer
    userfunction.o(.text.AdcToLcd) refers to lcd.o(.bss.LCD_Voltage) for LCD_Voltage
    userfunction.o(.text.AdcToLcd) refers to lcd.o(.text.Voltage_To_LCD) for Voltage_To_LCD
    userfunction.o(.ARM.exidx.text.AdcToLcd) refers to userfunction.o(.text.AdcToLcd) for [Anonymous Symbol]
    userfunction.o(.text.Signal_3p1D) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.ARM.exidx.text.Signal_3p1D) refers to userfunction.o(.text.Signal_3p1D) for [Anonymous Symbol]
    userfunction.o(.text.Signal_1D1p1D) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.ARM.exidx.text.Signal_1D1p1D) refers to userfunction.o(.text.Signal_1D1p1D) for [Anonymous Symbol]
    userfunction.o(.text.Signal_Beep) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.ARM.exidx.text.Signal_Beep) refers to userfunction.o(.text.Signal_Beep) for [Anonymous Symbol]
    userfunction.o(.text.Signal_OK) refers to userfunction.o(.text.Signal_Beep) for Signal_Beep
    userfunction.o(.text.Signal_OK) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.ARM.exidx.text.Signal_OK) refers to userfunction.o(.text.Signal_OK) for [Anonymous Symbol]
    userfunction.o(.text.Signal_NO) refers to userfunction.o(.text.Signal_Beep) for Signal_Beep
    userfunction.o(.text.Signal_NO) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.ARM.exidx.text.Signal_NO) refers to userfunction.o(.text.Signal_NO) for [Anonymous Symbol]
    userfunction.o(.ARM.exidx.text.Send_To_Main) refers to userfunction.o(.text.Send_To_Main) for [Anonymous Symbol]
    userfunction.o(.text.RotateM1_D14_Position) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.RotateM1_D14_Position) refers to main.o(.data.M1_StepDelay) for M1_StepDelay
    userfunction.o(.text.RotateM1_D14_Position) refers to main.o(.data.M1_PulseWidth) for M1_PulseWidth
    userfunction.o(.text.RotateM1_D14_Position) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.RotateM1_D14_Position) refers to userfunction.o(.rodata.str1.1) for .L.str.2
    userfunction.o(.text.RotateM1_D14_Position) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.RotateM1_D14_Position) refers to main.o(.bss.SensorPositionError) for SensorPositionError
    userfunction.o(.ARM.exidx.text.RotateM1_D14_Position) refers to userfunction.o(.text.RotateM1_D14_Position) for [Anonymous Symbol]
    userfunction.o(.text.RotateM2_D13_Position) refers to main.o(.data.M2_StepDelay_CW) for M2_StepDelay_CW
    userfunction.o(.text.RotateM2_D13_Position) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.RotateM2_D13_Position) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.RotateM2_D13_Position) refers to userfunction.o(.rodata.str1.1) for .L.str.5
    userfunction.o(.text.RotateM2_D13_Position) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.RotateM2_D13_Position) refers to main.o(.bss.SensorPositionError) for SensorPositionError
    userfunction.o(.ARM.exidx.text.RotateM2_D13_Position) refers to userfunction.o(.text.RotateM2_D13_Position) for [Anonymous Symbol]
    userfunction.o(.text.Rotate_M1_CW) refers to main.o(.bss.M1_Angele) for M1_Angele
    userfunction.o(.text.Rotate_M1_CW) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.Rotate_M1_CW) refers to main.o(.data.M1_StepDelay) for M1_StepDelay
    userfunction.o(.text.Rotate_M1_CW) refers to main.o(.data.M1_PulseWidth) for M1_PulseWidth
    userfunction.o(.text.Rotate_M1_CW) refers to main.o(.bss.Encoders_Angele) for Encoders_Angele
    userfunction.o(.text.Rotate_M1_CW) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Rotate_M1_CW) refers to userfunction.o(.rodata.str1.1) for .L.str.8
    userfunction.o(.text.Rotate_M1_CW) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Rotate_M1_CW) refers to main.o(.bss.SensorPositionError) for SensorPositionError
    userfunction.o(.ARM.exidx.text.Rotate_M1_CW) refers to userfunction.o(.text.Rotate_M1_CW) for [Anonymous Symbol]
    userfunction.o(.text.Rotate_M1_CCW) refers to main.o(.bss.M1_Angele) for M1_Angele
    userfunction.o(.text.Rotate_M1_CCW) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.Rotate_M1_CCW) refers to main.o(.data.M1_StepDelay) for M1_StepDelay
    userfunction.o(.text.Rotate_M1_CCW) refers to main.o(.data.M1_PulseWidth) for M1_PulseWidth
    userfunction.o(.text.Rotate_M1_CCW) refers to main.o(.bss.Encoders_Angele) for Encoders_Angele
    userfunction.o(.text.Rotate_M1_CCW) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Rotate_M1_CCW) refers to userfunction.o(.rodata.str1.1) for .L.str.8
    userfunction.o(.text.Rotate_M1_CCW) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Rotate_M1_CCW) refers to main.o(.bss.SensorPositionError) for SensorPositionError
    userfunction.o(.ARM.exidx.text.Rotate_M1_CCW) refers to userfunction.o(.text.Rotate_M1_CCW) for [Anonymous Symbol]
    userfunction.o(.text.Rotate_M2_CW) refers to main.o(.bss.M2_Angele) for M2_Angele
    userfunction.o(.text.Rotate_M2_CW) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.Rotate_M2_CW) refers to main.o(.data.M2_StepDelay_CW) for M2_StepDelay_CW
    userfunction.o(.text.Rotate_M2_CW) refers to main.o(.bss.Encoders_Angele) for Encoders_Angele
    userfunction.o(.ARM.exidx.text.Rotate_M2_CW) refers to userfunction.o(.text.Rotate_M2_CW) for [Anonymous Symbol]
    userfunction.o(.text.Rotate_M2_CCW) refers to main.o(.bss.M2_Angele) for M2_Angele
    userfunction.o(.text.Rotate_M2_CCW) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.Rotate_M2_CCW) refers to main.o(.data.M2_StepDelay_CCW) for M2_StepDelay_CCW
    userfunction.o(.text.Rotate_M2_CCW) refers to main.o(.data.M2_PulseWidth_CCW) for M2_PulseWidth_CCW
    userfunction.o(.text.Rotate_M2_CCW) refers to main.o(.data.M2_ExtraDelay_CCW) for M2_ExtraDelay_CCW
    userfunction.o(.text.Rotate_M2_CCW) refers to main.o(.bss.Encoders_Angele) for Encoders_Angele
    userfunction.o(.ARM.exidx.text.Rotate_M2_CCW) refers to userfunction.o(.text.Rotate_M2_CCW) for [Anonymous Symbol]
    userfunction.o(.text.Rotate_M3) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.Rotate_M3) refers to main.o(.data.M3_StepDelay_uS) for M3_StepDelay_uS
    userfunction.o(.text.Rotate_M3) refers to main.o(.text.Delay_uS) for Delay_uS
    userfunction.o(.text.Rotate_M3) refers to main.o(.data.M3_PulseWidth_uS) for M3_PulseWidth_uS
    userfunction.o(.text.Rotate_M3) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Rotate_M3) refers to userfunction.o(.rodata.str1.1) for .L.str.13
    userfunction.o(.text.Rotate_M3) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Rotate_M3) refers to main.o(.bss.SensorPositionError) for SensorPositionError
    userfunction.o(.ARM.exidx.text.Rotate_M3) refers to userfunction.o(.text.Rotate_M3) for [Anonymous Symbol]
    userfunction.o(.text.Rotate_M4) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.Rotate_M4) refers to main.o(.data.M4_StepDelay_uS) for M4_StepDelay_uS
    userfunction.o(.text.Rotate_M4) refers to main.o(.text.Delay_uS) for Delay_uS
    userfunction.o(.text.Rotate_M4) refers to main.o(.data.M4_PulseWidth_uS) for M4_PulseWidth_uS
    userfunction.o(.text.Rotate_M4) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Rotate_M4) refers to userfunction.o(.rodata.str1.1) for .L.str.17
    userfunction.o(.text.Rotate_M4) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Rotate_M4) refers to main.o(.bss.SensorPositionError) for SensorPositionError
    userfunction.o(.ARM.exidx.text.Rotate_M4) refers to userfunction.o(.text.Rotate_M4) for [Anonymous Symbol]
    userfunction.o(.text.Rotate_M5) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.Rotate_M5) refers to main.o(.data.M5_StepDelay_uS) for M5_StepDelay_uS
    userfunction.o(.text.Rotate_M5) refers to main.o(.text.Delay_uS) for Delay_uS
    userfunction.o(.text.Rotate_M5) refers to main.o(.data.M5_PulseWidth_uS) for M5_PulseWidth_uS
    userfunction.o(.text.Rotate_M5) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Rotate_M5) refers to userfunction.o(.rodata.str1.1) for .L.str.21
    userfunction.o(.text.Rotate_M5) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Rotate_M5) refers to main.o(.bss.SensorPositionError) for SensorPositionError
    userfunction.o(.ARM.exidx.text.Rotate_M5) refers to userfunction.o(.text.Rotate_M5) for [Anonymous Symbol]
    userfunction.o(.text.Rotate_M6) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.Rotate_M6) refers to main.o(.data.M6_StepDelay) for M6_StepDelay
    userfunction.o(.text.Rotate_M6) refers to main.o(.data.M6_PulseWidth) for M6_PulseWidth
    userfunction.o(.text.Rotate_M6) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Rotate_M6) refers to userfunction.o(.rodata.str1.1) for .L.str.25
    userfunction.o(.text.Rotate_M6) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Rotate_M6) refers to main.o(.bss.projectile_number) for projectile_number
    userfunction.o(.text.Rotate_M6) refers to main.o(.bss.SensorPositionError) for SensorPositionError
    userfunction.o(.ARM.exidx.text.Rotate_M6) refers to userfunction.o(.text.Rotate_M6) for [Anonymous Symbol]
    userfunction.o(.text.Rotate_M6_Step) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.Rotate_M6_Step) refers to main.o(.data.M6_StepDelay) for M6_StepDelay
    userfunction.o(.text.Rotate_M6_Step) refers to main.o(.data.M6_PulseWidth) for M6_PulseWidth
    userfunction.o(.ARM.exidx.text.Rotate_M6_Step) refers to userfunction.o(.text.Rotate_M6_Step) for [Anonymous Symbol]
    userfunction.o(.text.Rotate_M6_Step_Safe) refers to main.o(.data.M6_StepDelay) for M6_StepDelay
    userfunction.o(.text.Rotate_M6_Step_Safe) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Rotate_M6_Step_Safe) refers to userfunction.o(.rodata.str1.1) for .L.str.28
    userfunction.o(.text.Rotate_M6_Step_Safe) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Rotate_M6_Step_Safe) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.Rotate_M6_Step_Safe) refers to userfunction.o(.bss.Rotate_M6_Step_Safe.step_counter) for Rotate_M6_Step_Safe.step_counter
    userfunction.o(.text.Rotate_M6_Step_Safe) refers to main.o(.text.Delay_uS) for Delay_uS
    userfunction.o(.text.Rotate_M6_Step_Safe) refers to main.o(.bss.projectile_number) for projectile_number
    userfunction.o(.ARM.exidx.text.Rotate_M6_Step_Safe) refers to userfunction.o(.text.Rotate_M6_Step_Safe) for [Anonymous Symbol]
    userfunction.o(.text.Rotate_M7) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Rotate_M7) refers to userfunction.o(.rodata.str1.1) for .L.str.33
    userfunction.o(.text.Rotate_M7) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Rotate_M7) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.Rotate_M7) refers to main.o(.bss.M7_Error) for M7_Error
    userfunction.o(.ARM.exidx.text.Rotate_M7) refers to userfunction.o(.text.Rotate_M7) for [Anonymous Symbol]
    userfunction.o(.text.GetEncoder_1_Angele) refers to main.o(.bss.Encoders_Angele) for Encoders_Angele
    userfunction.o(.text.GetEncoder_1_Angele) refers to main.o(.bss.M1_Angele) for M1_Angele
    userfunction.o(.ARM.exidx.text.GetEncoder_1_Angele) refers to userfunction.o(.text.GetEncoder_1_Angele) for [Anonymous Symbol]
    userfunction.o(.text.GetEncoder_2_Angele) refers to main.o(.bss.Encoders_Angele) for Encoders_Angele
    userfunction.o(.text.GetEncoder_2_Angele) refers to main.o(.bss.M2_Angele) for M2_Angele
    userfunction.o(.ARM.exidx.text.GetEncoder_2_Angele) refers to userfunction.o(.text.GetEncoder_2_Angele) for [Anonymous Symbol]
    userfunction.o(.text.LoadUp) refers to main.o(.bss.SensorPositionError) for SensorPositionError
    userfunction.o(.text.LoadUp) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.LoadUp) refers to userfunction.o(.rodata.str1.1) for .L.str.39
    userfunction.o(.text.LoadUp) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.LoadUp) refers to userfunction.o(.text.Rotate_M3) for Rotate_M3
    userfunction.o(.text.LoadUp) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.LoadUp) refers to userfunction.o(.text.Rotate_M7) for Rotate_M7
    userfunction.o(.text.LoadUp) refers to main.o(.bss.M7_Error) for M7_Error
    userfunction.o(.text.LoadUp) refers to userfunction.o(.text.Rotate_M4) for Rotate_M4
    userfunction.o(.text.LoadUp) refers to userfunction.o(.text.Rotate_M5) for Rotate_M5
    userfunction.o(.text.LoadUp) refers to main.o(.bss.u8_ReceivedCommand) for u8_ReceivedCommand
    userfunction.o(.text.LoadUp) refers to userfunction.o(.text.Send_To_Main) for Send_To_Main
    userfunction.o(.text.LoadUp) refers to userfunction.o(.text.Rotate_M6) for Rotate_M6
    userfunction.o(.ARM.exidx.text.LoadUp) refers to userfunction.o(.text.LoadUp) for [Anonymous Symbol]
    userfunction.o(.text.Rotate_M6_Stage) refers to userfunction.o(.text.Rotate_M6_Step) for Rotate_M6_Step
    userfunction.o(.ARM.exidx.text.Rotate_M6_Stage) refers to userfunction.o(.text.Rotate_M6_Stage) for [Anonymous Symbol]
    userfunction.o(.text.Rotate_M6_Max_Power) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.Rotate_M6_Max_Power) refers to main.o(.data.M6_StepDelay) for M6_StepDelay
    userfunction.o(.text.Rotate_M6_Max_Power) refers to main.o(.data.M6_PulseWidth) for M6_PulseWidth
    userfunction.o(.ARM.exidx.text.Rotate_M6_Max_Power) refers to userfunction.o(.text.Rotate_M6_Max_Power) for [Anonymous Symbol]
    userfunction.o(.text.Ready_Command) refers to userfunction.o(.text.Timer_Start) for Timer_Start
    userfunction.o(.text.Ready_Command) refers to userfunction.o(.rodata.str1.1) for .L.str.55
    userfunction.o(.text.Ready_Command) refers to userfunction.o(.text.Timer_Stop_And_Show) for Timer_Stop_And_Show
    userfunction.o(.text.Ready_Command) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Ready_Command) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Ready_Command) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.Ready_Command) refers to userfunction.o(.text.Rotate_M3_Adaptive) for Rotate_M3_Adaptive
    userfunction.o(.text.Ready_Command) refers to userfunction.o(.text.Rotate_M5) for Rotate_M5
    userfunction.o(.text.Ready_Command) refers to userfunction.o(.text.M2_Hold_Position_Active) for M2_Hold_Position_Active
    userfunction.o(.text.Ready_Command) refers to userfunction.o(.text.Show_All_Motors_Status) for Show_All_Motors_Status
    userfunction.o(.ARM.exidx.text.Ready_Command) refers to userfunction.o(.text.Ready_Command) for [Anonymous Symbol]
    userfunction.o(.text.Timer_Start) refers to userfunction.o(.text.Get_System_MS) for Get_System_MS
    userfunction.o(.text.Timer_Start) refers to userfunction.o(.bss.g_timer_start_ms) for g_timer_start_ms
    userfunction.o(.text.Timer_Start) refers to userfunction.o(.bss.g_timer_running) for g_timer_running
    userfunction.o(.text.Timer_Start) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Timer_Start) refers to userfunction.o(.rodata.str1.1) for .L.str.109
    userfunction.o(.text.Timer_Start) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.ARM.exidx.text.Timer_Start) refers to userfunction.o(.text.Timer_Start) for [Anonymous Symbol]
    userfunction.o(.text.Timer_Stop_And_Show) refers to userfunction.o(.bss.g_timer_running) for g_timer_running
    userfunction.o(.text.Timer_Stop_And_Show) refers to userfunction.o(.text.Get_System_MS) for Get_System_MS
    userfunction.o(.text.Timer_Stop_And_Show) refers to userfunction.o(.bss.g_timer_current_ms) for g_timer_current_ms
    userfunction.o(.text.Timer_Stop_And_Show) refers to userfunction.o(.bss.g_timer_start_ms) for g_timer_start_ms
    userfunction.o(.text.Timer_Stop_And_Show) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Timer_Stop_And_Show) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Timer_Stop_And_Show) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.ARM.exidx.text.Timer_Stop_And_Show) refers to userfunction.o(.text.Timer_Stop_And_Show) for [Anonymous Symbol]
    userfunction.o(.text.Rotate_M3_Adaptive) refers to main.o(.data.M3_StepDelay_uS) for M3_StepDelay_uS
    userfunction.o(.text.Rotate_M3_Adaptive) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.Rotate_M3_Adaptive) refers to main.o(.text.Delay_uS) for Delay_uS
    userfunction.o(.text.Rotate_M3_Adaptive) refers to main.o(.data.M3_PulseWidth_uS) for M3_PulseWidth_uS
    userfunction.o(.text.Rotate_M3_Adaptive) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Rotate_M3_Adaptive) refers to userfunction.o(.rodata.str1.1) for .L.str.99
    userfunction.o(.text.Rotate_M3_Adaptive) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.ARM.exidx.text.Rotate_M3_Adaptive) refers to userfunction.o(.text.Rotate_M3_Adaptive) for [Anonymous Symbol]
    userfunction.o(.text.M2_Hold_Position_Active) refers to userfunction.o(.bss.position_hold_active) for position_hold_active
    userfunction.o(.text.M2_Hold_Position_Active) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.M2_Hold_Position_Active) refers to userfunction.o(.rodata.str1.1) for .L.str.110
    userfunction.o(.text.M2_Hold_Position_Active) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.ARM.exidx.text.M2_Hold_Position_Active) refers to userfunction.o(.text.M2_Hold_Position_Active) for [Anonymous Symbol]
    userfunction.o(.text.Show_All_Motors_Status) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Show_All_Motors_Status) refers to userfunction.o(.rodata.str1.1) for .L.str.112
    userfunction.o(.text.Show_All_Motors_Status) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Show_All_Motors_Status) refers to userfunction.o(.bss.motor_stats) for motor_stats
    userfunction.o(.text.Show_All_Motors_Status) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.ARM.exidx.text.Show_All_Motors_Status) refers to userfunction.o(.text.Show_All_Motors_Status) for [Anonymous Symbol]
    userfunction.o(.text.Return_All_Motors_Home) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Return_All_Motors_Home) refers to userfunction.o(.rodata.str1.1) for .L.str.62
    userfunction.o(.text.Return_All_Motors_Home) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Return_All_Motors_Home) refers to userfunction.o(.text.RotateM1_D14_Position) for RotateM1_D14_Position
    userfunction.o(.text.Return_All_Motors_Home) refers to userfunction.o(.text.RotateM2_D13_Position) for RotateM2_D13_Position
    userfunction.o(.text.Return_All_Motors_Home) refers to userfunction.o(.text.Rotate_M3) for Rotate_M3
    userfunction.o(.text.Return_All_Motors_Home) refers to userfunction.o(.text.Rotate_M4) for Rotate_M4
    userfunction.o(.text.Return_All_Motors_Home) refers to userfunction.o(.text.Rotate_M5) for Rotate_M5
    userfunction.o(.text.Return_All_Motors_Home) refers to userfunction.o(.text.Rotate_M6) for Rotate_M6
    userfunction.o(.text.Return_All_Motors_Home) refers to userfunction.o(.text.Rotate_M7) for Rotate_M7
    userfunction.o(.ARM.exidx.text.Return_All_Motors_Home) refers to userfunction.o(.text.Return_All_Motors_Home) for [Anonymous Symbol]
    userfunction.o(.text.Test_M1_Simple) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Test_M1_Simple) refers to userfunction.o(.rodata.str1.1) for .L.str.71
    userfunction.o(.text.Test_M1_Simple) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Test_M1_Simple) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.Test_M1_Simple) refers to main.o(.data.M1_StepDelay) for M1_StepDelay
    userfunction.o(.text.Test_M1_Simple) refers to main.o(.data.M1_PulseWidth) for M1_PulseWidth
    userfunction.o(.ARM.exidx.text.Test_M1_Simple) refers to userfunction.o(.text.Test_M1_Simple) for [Anonymous Symbol]
    userfunction.o(.text.Test_All_Motors_Max_Speed) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Test_All_Motors_Max_Speed) refers to userfunction.o(.rodata.str1.1) for .L.str.74
    userfunction.o(.text.Test_All_Motors_Max_Speed) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Test_All_Motors_Max_Speed) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.Test_All_Motors_Max_Speed) refers to main.o(.text.Delay_uS) for Delay_uS
    userfunction.o(.ARM.exidx.text.Test_All_Motors_Max_Speed) refers to userfunction.o(.text.Test_All_Motors_Max_Speed) for [Anonymous Symbol]
    userfunction.o(.text.Play_Note) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.Play_Note) refers to main.o(.text.Delay_uS) for Delay_uS
    userfunction.o(.ARM.exidx.text.Play_Note) refers to userfunction.o(.text.Play_Note) for [Anonymous Symbol]
    userfunction.o(.text.Play_Piano_Melody) refers to userfunction.o(.rodata..L__const.Play_Piano_Melody.notes) for .L__const.Play_Piano_Melody.notes
    userfunction.o(.text.Play_Piano_Melody) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    userfunction.o(.text.Play_Piano_Melody) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Play_Piano_Melody) refers to userfunction.o(.rodata.str1.1) for .L.str.81
    userfunction.o(.text.Play_Piano_Melody) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Play_Piano_Melody) refers to userfunction.o(.text.Play_Note) for Play_Note
    userfunction.o(.text.Play_Piano_Melody) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.ARM.exidx.text.Play_Piano_Melody) refers to userfunction.o(.text.Play_Piano_Melody) for [Anonymous Symbol]
    userfunction.o(.text.Show_About_Page) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Show_About_Page) refers to userfunction.o(.rodata.str1.1) for .L.str.83
    userfunction.o(.text.Show_About_Page) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Show_About_Page) refers to userfunction.o(.text.Play_Piano_Melody) for Play_Piano_Melody
    userfunction.o(.text.Show_About_Page) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.ARM.exidx.text.Show_About_Page) refers to userfunction.o(.text.Show_About_Page) for [Anonymous Symbol]
    userfunction.o(.text.Load_Embedded_Config) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Load_Embedded_Config) refers to userfunction.o(.rodata.str1.1) for .L.str.100
    userfunction.o(.text.Load_Embedded_Config) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Load_Embedded_Config) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.ARM.exidx.text.Load_Embedded_Config) refers to userfunction.o(.text.Load_Embedded_Config) for [Anonymous Symbol]
    userfunction.o(.text.Show_Current_Config) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Show_Current_Config) refers to userfunction.o(.rodata.str1.1) for .L.str.102
    userfunction.o(.text.Show_Current_Config) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Show_Current_Config) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.ARM.exidx.text.Show_Current_Config) refers to userfunction.o(.text.Show_Current_Config) for [Anonymous Symbol]
    userfunction.o(.text.Get_System_MS) refers to userfunction.o(.bss.g_system_ms_counter) for g_system_ms_counter
    userfunction.o(.ARM.exidx.text.Get_System_MS) refers to userfunction.o(.text.Get_System_MS) for [Anonymous Symbol]
    userfunction.o(.text.Timer_Reset) refers to userfunction.o(.bss.g_timer_start_ms) for g_timer_start_ms
    userfunction.o(.text.Timer_Reset) refers to userfunction.o(.bss.g_timer_current_ms) for g_timer_current_ms
    userfunction.o(.text.Timer_Reset) refers to userfunction.o(.bss.g_timer_running) for g_timer_running
    userfunction.o(.ARM.exidx.text.Timer_Reset) refers to userfunction.o(.text.Timer_Reset) for [Anonymous Symbol]
    userfunction.o(.text.Timer_Get_Elapsed_MS) refers to userfunction.o(.bss.g_timer_running) for g_timer_running
    userfunction.o(.text.Timer_Get_Elapsed_MS) refers to userfunction.o(.text.Get_System_MS) for Get_System_MS
    userfunction.o(.text.Timer_Get_Elapsed_MS) refers to userfunction.o(.bss.g_timer_start_ms) for g_timer_start_ms
    userfunction.o(.ARM.exidx.text.Timer_Get_Elapsed_MS) refers to userfunction.o(.text.Timer_Get_Elapsed_MS) for [Anonymous Symbol]
    userfunction.o(.text.M2_Hold_Position_Inactive) refers to userfunction.o(.bss.position_hold_active) for position_hold_active
    userfunction.o(.text.M2_Hold_Position_Inactive) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.M2_Hold_Position_Inactive) refers to userfunction.o(.rodata.str1.1) for .L.str.111
    userfunction.o(.text.M2_Hold_Position_Inactive) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.ARM.exidx.text.M2_Hold_Position_Inactive) refers to userfunction.o(.text.M2_Hold_Position_Inactive) for [Anonymous Symbol]
    userfunction.o(.text.M2_Hold_Position) refers to userfunction.o(.bss.position_hold_active) for position_hold_active
    userfunction.o(.text.M2_Hold_Position) refers to main.o(.data.M2_StepDelay_CCW) for M2_StepDelay_CCW
    userfunction.o(.text.M2_Hold_Position) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.text.M2_Hold_Position) refers to main.o(.data.M2_PulseWidth_CCW) for M2_PulseWidth_CCW
    userfunction.o(.ARM.exidx.text.M2_Hold_Position) refers to userfunction.o(.text.M2_Hold_Position) for [Anonymous Symbol]
    userfunction.o(.text.Update_Motor_Stats) refers to userfunction.o(.bss.motor_stats) for motor_stats
    userfunction.o(.ARM.exidx.text.Update_Motor_Stats) refers to userfunction.o(.text.Update_Motor_Stats) for [Anonymous Symbol]
    userfunction.o(.text.Test_Motor_Stability) refers to userfunction.o(.text.Get_System_MS) for Get_System_MS
    userfunction.o(.text.Test_Motor_Stability) refers to main.o(.text.Delay_uS) for Delay_uS
    userfunction.o(.ARM.exidx.text.Test_Motor_Stability) refers to userfunction.o(.text.Test_Motor_Stability) for [Anonymous Symbol]
    userfunction.o(.text.Auto_Calibrate_All_Motors) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Auto_Calibrate_All_Motors) refers to userfunction.o(.rodata.str1.1) for .L.str.114
    userfunction.o(.text.Auto_Calibrate_All_Motors) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Auto_Calibrate_All_Motors) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.ARM.exidx.text.Auto_Calibrate_All_Motors) refers to userfunction.o(.text.Auto_Calibrate_All_Motors) for [Anonymous Symbol]
    userfunction.o(.text.Find_Optimal_Speed) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Find_Optimal_Speed) refers to userfunction.o(.rodata.str1.1) for .L.str.120
    userfunction.o(.text.Find_Optimal_Speed) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Find_Optimal_Speed) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.ARM.exidx.text.Find_Optimal_Speed) refers to userfunction.o(.text.Find_Optimal_Speed) for [Anonymous Symbol]
    userfunction.o(.text.Test_Motor_At_Speed) refers to userfunction.o(.text.Get_System_MS) for Get_System_MS
    userfunction.o(.text.Test_Motor_At_Speed) refers to main.o(.text.Delay_uS) for Delay_uS
    userfunction.o(.ARM.exidx.text.Test_Motor_At_Speed) refers to userfunction.o(.text.Test_Motor_At_Speed) for [Anonymous Symbol]
    userfunction.o(.text.Test_Motor_Reliability) refers to userfunction.o(.text.Test_Motor_At_Speed) for Test_Motor_At_Speed
    userfunction.o(.text.Test_Motor_Reliability) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.ARM.exidx.text.Test_Motor_Reliability) refers to userfunction.o(.text.Test_Motor_Reliability) for [Anonymous Symbol]
    userfunction.o(.text.Save_Motor_Speed) refers to main.o(.data.M1_StepDelay) for M1_StepDelay
    userfunction.o(.text.Save_Motor_Speed) refers to main.o(.data.M1_PulseWidth) for M1_PulseWidth
    userfunction.o(.text.Save_Motor_Speed) refers to main.o(.data.M2_StepDelay_CW) for M2_StepDelay_CW
    userfunction.o(.text.Save_Motor_Speed) refers to main.o(.data.M2_StepDelay_CCW) for M2_StepDelay_CCW
    userfunction.o(.text.Save_Motor_Speed) refers to main.o(.data.M3_StepDelay) for M3_StepDelay
    userfunction.o(.text.Save_Motor_Speed) refers to main.o(.data.M3_PulseWidth) for M3_PulseWidth
    userfunction.o(.text.Save_Motor_Speed) refers to main.o(.data.M4_StepDelay) for M4_StepDelay
    userfunction.o(.text.Save_Motor_Speed) refers to main.o(.data.M4_PulseWidth) for M4_PulseWidth
    userfunction.o(.text.Save_Motor_Speed) refers to main.o(.data.M5_StepDelay) for M5_StepDelay
    userfunction.o(.text.Save_Motor_Speed) refers to main.o(.data.M5_PulseWidth) for M5_PulseWidth
    userfunction.o(.ARM.exidx.text.Save_Motor_Speed) refers to userfunction.o(.text.Save_Motor_Speed) for [Anonymous Symbol]
    userfunction.o(.text.Init_Motor_Health) refers to userfunction.o(.bss.motor_health) for motor_health
    userfunction.o(.ARM.exidx.text.Init_Motor_Health) refers to userfunction.o(.text.Init_Motor_Health) for [Anonymous Symbol]
    userfunction.o(.text.Check_Maintenance_Needs) refers to userfunction.o(.bss.motor_health) for motor_health
    userfunction.o(.text.Check_Maintenance_Needs) refers to userfunction.o(.bss.motor_stats) for motor_stats
    userfunction.o(.text.Check_Maintenance_Needs) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Check_Maintenance_Needs) refers to userfunction.o(.rodata.str1.1) for .L.str.122
    userfunction.o(.text.Check_Maintenance_Needs) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Check_Maintenance_Needs) refers to userfunction.o(.text.Generate_Maintenance_Alert) for Generate_Maintenance_Alert
    userfunction.o(.ARM.exidx.text.Check_Maintenance_Needs) refers to userfunction.o(.text.Check_Maintenance_Needs) for [Anonymous Symbol]
    userfunction.o(.text.Generate_Maintenance_Alert) refers to userfunction.o(.text.Signal_Beep) for Signal_Beep
    userfunction.o(.text.Generate_Maintenance_Alert) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    userfunction.o(.text.Generate_Maintenance_Alert) refers to userfunction.o(.rodata.str1.1) for .L.str.124
    userfunction.o(.text.Generate_Maintenance_Alert) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    userfunction.o(.text.Generate_Maintenance_Alert) refers to main.o(.text.Delay_mS) for Delay_mS
    userfunction.o(.ARM.exidx.text.Generate_Maintenance_Alert) refers to userfunction.o(.text.Generate_Maintenance_Alert) for [Anonymous Symbol]
    json_parser.o(.text.JSON_StringToCommand) refers to json_parser.o(.rodata.str1.1) for .L.str
    json_parser.o(.text.JSON_StringToCommand) refers to json_parser.o(.text.simple_strcmp) for simple_strcmp
    json_parser.o(.ARM.exidx.text.JSON_StringToCommand) refers to json_parser.o(.text.JSON_StringToCommand) for [Anonymous Symbol]
    json_parser.o(.ARM.exidx.text.simple_strcmp) refers to json_parser.o(.text.simple_strcmp) for [Anonymous Symbol]
    json_parser.o(.text.JSON_GetCommandName) refers to json_parser.o(.rodata.str1.1) for .L.str
    json_parser.o(.ARM.exidx.text.JSON_GetCommandName) refers to json_parser.o(.text.JSON_GetCommandName) for [Anonymous Symbol]
    json_parser.o(.text.JSON_GetStringValue) refers to json_parser.o(.text.simple_strlen) for simple_strlen
    json_parser.o(.ARM.exidx.text.JSON_GetStringValue) refers to json_parser.o(.text.JSON_GetStringValue) for [Anonymous Symbol]
    json_parser.o(.ARM.exidx.text.simple_strlen) refers to json_parser.o(.text.simple_strlen) for [Anonymous Symbol]
    json_parser.o(.text.JSON_GetIntValue) refers to json_parser.o(.text.JSON_GetStringValue) for JSON_GetStringValue
    json_parser.o(.ARM.exidx.text.JSON_GetIntValue) refers to json_parser.o(.text.JSON_GetIntValue) for [Anonymous Symbol]
    json_parser.o(.text.JSON_Parse) refers to json_parser.o(.rodata.str1.1) for .L.str.18
    json_parser.o(.text.JSON_Parse) refers to json_parser.o(.text.JSON_GetStringValue) for JSON_GetStringValue
    json_parser.o(.text.JSON_Parse) refers to json_parser.o(.text.JSON_StringToCommand) for JSON_StringToCommand
    json_parser.o(.text.JSON_Parse) refers to json_parser.o(.text.JSON_GetIntValue) for JSON_GetIntValue
    json_parser.o(.ARM.exidx.text.JSON_Parse) refers to json_parser.o(.text.JSON_Parse) for [Anonymous Symbol]
    json_parser.o(.text.JSON_ExecuteCommand) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    json_parser.o(.text.JSON_ExecuteCommand) refers to json_parser.o(.rodata.str1.1) for .L.str.29
    json_parser.o(.text.JSON_ExecuteCommand) refers to lcd.o(.text.LCD_SendString) for LCD_SendString
    json_parser.o(.text.JSON_ExecuteCommand) refers to userfunction.o(.text.Return_All_Motors_Home) for Return_All_Motors_Home
    json_parser.o(.text.JSON_ExecuteCommand) refers to userfunction.o(.text.Ready_Command) for Ready_Command
    json_parser.o(.text.JSON_ExecuteCommand) refers to userfunction.o(.text.Show_About_Page) for Show_About_Page
    json_parser.o(.text.JSON_ExecuteCommand) refers to userfunction.o(.text.Play_Piano_Melody) for Play_Piano_Melody
    json_parser.o(.text.JSON_ExecuteCommand) refers to userfunction.o(.text.Rotate_M6_Step_Safe) for Rotate_M6_Step_Safe
    json_parser.o(.text.JSON_ExecuteCommand) refers to userfunction.o(.text.Test_All_Motors_Max_Speed) for Test_All_Motors_Max_Speed
    json_parser.o(.text.JSON_ExecuteCommand) refers to userfunction.o(.text.Load_Embedded_Config) for Load_Embedded_Config
    json_parser.o(.text.JSON_ExecuteCommand) refers to userfunction.o(.text.Show_Current_Config) for Show_Current_Config
    json_parser.o(.text.JSON_ExecuteCommand) refers to userfunction.o(.text.Auto_Calibrate_All_Motors) for Auto_Calibrate_All_Motors
    json_parser.o(.ARM.exidx.text.JSON_ExecuteCommand) refers to json_parser.o(.text.JSON_ExecuteCommand) for [Anonymous Symbol]
    io_gpio.o(.ARM.exidx.text.SetupGpioIO) refers to io_gpio.o(.text.SetupGpioIO) for [Anonymous Symbol]
    rcc.o(.ARM.exidx.text.RCCInit) refers to rcc.o(.text.RCCInit) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.TIM2_IRQHandler) refers to timers.o(.text.TIM2_IRQHandler) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.SetupTimers) refers to timers.o(.text.SetupTimers) for [Anonymous Symbol]
    timers.o(.ARM.exidx.text.TIM4_IRQHandler) refers to timers.o(.text.TIM4_IRQHandler) for [Anonymous Symbol]
    i2c.o(.ARM.exidx.text.SetUp_I2C1) refers to i2c.o(.text.SetUp_I2C1) for [Anonymous Symbol]
    i2c.o(.ARM.exidx.text.I2C1_Start) refers to i2c.o(.text.I2C1_Start) for [Anonymous Symbol]
    i2c.o(.ARM.exidx.text.I2C1_AddrSend_Transmit) refers to i2c.o(.text.I2C1_AddrSend_Transmit) for [Anonymous Symbol]
    i2c.o(.ARM.exidx.text.I2C1_AddrSend_Receive) refers to i2c.o(.text.I2C1_AddrSend_Receive) for [Anonymous Symbol]
    lcd.o(.text.LCD_Send_Command) refers to main.o(.text.Delay_mS) for Delay_mS
    lcd.o(.ARM.exidx.text.LCD_Send_Command) refers to lcd.o(.text.LCD_Send_Command) for [Anonymous Symbol]
    lcd.o(.text.LCD_Send_4BitCmd) refers to main.o(.text.Delay_mS) for Delay_mS
    lcd.o(.ARM.exidx.text.LCD_Send_4BitCmd) refers to lcd.o(.text.LCD_Send_4BitCmd) for [Anonymous Symbol]
    lcd.o(.text.LCD_Send_Data) refers to main.o(.text.Delay_mS) for Delay_mS
    lcd.o(.ARM.exidx.text.LCD_Send_Data) refers to lcd.o(.text.LCD_Send_Data) for [Anonymous Symbol]
    lcd.o(.text.LCD_Setup) refers to lcd.o(.text.LCD_Send_4BitCmd) for LCD_Send_4BitCmd
    lcd.o(.text.LCD_Setup) refers to main.o(.text.Delay_mS) for Delay_mS
    lcd.o(.text.LCD_Setup) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    lcd.o(.ARM.exidx.text.LCD_Setup) refers to lcd.o(.text.LCD_Setup) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.Test_1) refers to lcd.o(.text.Test_1) for [Anonymous Symbol]
    lcd.o(.text.LCD_CursorGoToLeft) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    lcd.o(.text.LCD_CursorGoToLeft) refers to main.o(.text.Delay_mS) for Delay_mS
    lcd.o(.ARM.exidx.text.LCD_CursorGoToLeft) refers to lcd.o(.text.LCD_CursorGoToLeft) for [Anonymous Symbol]
    lcd.o(.text.LCD_CursorGoToRight) refers to lcd.o(.text.LCD_Send_Command) for LCD_Send_Command
    lcd.o(.text.LCD_CursorGoToRight) refers to main.o(.text.Delay_mS) for Delay_mS
    lcd.o(.ARM.exidx.text.LCD_CursorGoToRight) refers to lcd.o(.text.LCD_CursorGoToRight) for [Anonymous Symbol]
    lcd.o(.text.LCD_SendString) refers to lcd.o(.text.LCD_Send_Data) for LCD_Send_Data
    lcd.o(.ARM.exidx.text.LCD_SendString) refers to lcd.o(.text.LCD_SendString) for [Anonymous Symbol]
    lcd.o(.text.LCD_Set_CGRAM) refers to main.o(.text.Delay_mS) for Delay_mS
    lcd.o(.ARM.exidx.text.LCD_Set_CGRAM) refers to lcd.o(.text.LCD_Set_CGRAM) for [Anonymous Symbol]
    lcd.o(.text.LCD_Write_CGRAM) refers to main.o(.text.Delay_mS) for Delay_mS
    lcd.o(.ARM.exidx.text.LCD_Write_CGRAM) refers to lcd.o(.text.LCD_Write_CGRAM) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.Current_To_LCD) refers to lcd.o(.text.Current_To_LCD) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.Voltage_To_LCD) refers to lcd.o(.text.Voltage_To_LCD) for [Anonymous Symbol]
    lcd.o(.ARM.exidx.text.LCD_UserChars) refers to lcd.o(.text.LCD_UserChars) for [Anonymous Symbol]
    startup_stm32f10x_hd.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to timers.o(.text.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to timers.o(.text.TIM4_IRQHandler) for TIM4_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to main.o(.text.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to main.o(.text.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f10x_hd.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(.text.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(HEAP) for Heap_Mem
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(STACK) for Stack_Mem
    system_stm32f10x.o(.text.SystemInit) refers to system_stm32f10x.o(.text.SetSysClock) for SetSysClock
    system_stm32f10x.o(.ARM.exidx.text.SystemInit) refers to system_stm32f10x.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f10x.o(.text.SetSysClock) refers to system_stm32f10x.o(.text.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(.ARM.exidx.text.SetSysClock) refers to system_stm32f10x.o(.text.SetSysClock) for [Anonymous Symbol]
    system_stm32f10x.o(.text.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f10x.o(.text.SystemCoreClockUpdate) refers to system_stm32f10x.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f10x.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f10x.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    system_stm32f10x.o(.ARM.exidx.text.SetSysClockTo72) refers to system_stm32f10x.o(.text.SetSysClockTo72) for [Anonymous Symbol]
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fcmp.o(x$fpl$fcmp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(x$fpl$fcmp) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(x$fpl$fcmp) refers to fgeqf.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(x$fpl$fcmp) refers to fleqf.o(x$fpl$fleqf) for _fcmple
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffixr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    feqf.o(x$fpl$feqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fgeqf.o(x$fpl$fgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fgeqf.o(x$fpl$fgeqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fgeqf.o(x$fpl$fgeqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fleqf.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fleqf.o(x$fpl$fleqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_hd.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.exidx.text.Delay_mS), (8 bytes).
    Removing main.o(.ARM.exidx.text.NVIC_EnableIRQ), (8 bytes).
    Removing main.o(.ARM.exidx.text.NVIC_SetPriority), (8 bytes).
    Removing main.o(.ARM.exidx.text.__enable_irq), (8 bytes).
    Removing main.o(.ARM.exidx.text.SaveReceivedCommand), (8 bytes).
    Removing main.o(.ARM.exidx.text.ClearCmdBuffer), (8 bytes).
    Removing main.o(.ARM.exidx.text.Delay_uS), (8 bytes).
    Removing main.o(.text.Int_To_Char), (134 bytes).
    Removing main.o(.ARM.exidx.text.Int_To_Char), (8 bytes).
    Removing main.o(.ARM.exidx.text.USART1_IRQHandler), (8 bytes).
    Removing main.o(.ARM.exidx.text.USART2_IRQHandler), (8 bytes).
    Removing main.o(.bss.T_Impulse), (1 bytes).
    Removing main.o(.bss.T_Pause), (1 bytes).
    Removing main.o(.data.M1_MaxSpeed), (2 bytes).
    Removing main.o(.data.M2_MaxSpeed), (2 bytes).
    Removing main.o(.data.M3_StartStepTime), (2 bytes).
    Removing main.o(.data.M3_StopStepTime), (2 bytes).
    Removing main.o(.data.M3_DeltaStepTime), (2 bytes).
    Removing main.o(.data.M3_StepNumber), (2 bytes).
    Removing main.o(.data.M3_StepDelay), (2 bytes).
    Removing main.o(.data.M3_PulseWidth), (2 bytes).
    Removing main.o(.data.M3_MaxSpeed), (2 bytes).
    Removing main.o(.data.M4_StartStepTime), (2 bytes).
    Removing main.o(.data.M4_StopStepTime), (2 bytes).
    Removing main.o(.data.M4_DeltaStepTime), (2 bytes).
    Removing main.o(.data.M4_StepNumber), (2 bytes).
    Removing main.o(.data.M4_StepDelay), (2 bytes).
    Removing main.o(.data.M4_PulseWidth), (2 bytes).
    Removing main.o(.data.M4_MaxSpeed), (2 bytes).
    Removing main.o(.data.M5_StepDelay), (2 bytes).
    Removing main.o(.data.M5_PulseWidth), (2 bytes).
    Removing main.o(.data.M5_MaxSpeed), (2 bytes).
    Removing main.o(.data.M6_MaxSpeed), (2 bytes).
    Removing main.o(.data.M7_Timeout), (2 bytes).
    Removing main.o(.data.M7_CheckDelay), (2 bytes).
    Removing main.o(.bss.Encoder1_Position), (2 bytes).
    Removing main.o(.bss.Encoder2_Position), (2 bytes).
    Removing main.o(.rodata.cst4), (4 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing userfunction.o(.text), (0 bytes).
    Removing userfunction.o(.ARM.exidx.text.ADC1_Config), (8 bytes).
    Removing userfunction.o(.text.ADC1_Connvert), (136 bytes).
    Removing userfunction.o(.ARM.exidx.text.ADC1_Connvert), (8 bytes).
    Removing userfunction.o(.text.GetAdc_CH0CH1), (64 bytes).
    Removing userfunction.o(.ARM.exidx.text.GetAdc_CH0CH1), (8 bytes).
    Removing userfunction.o(.text.AdcToLcd), (520 bytes).
    Removing userfunction.o(.ARM.exidx.text.AdcToLcd), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Signal_3p1D), (8 bytes).
    Removing userfunction.o(.text.Signal_1D1p1D), (122 bytes).
    Removing userfunction.o(.ARM.exidx.text.Signal_1D1p1D), (8 bytes).
    Removing userfunction.o(.text.Signal_Beep), (48 bytes).
    Removing userfunction.o(.ARM.exidx.text.Signal_Beep), (8 bytes).
    Removing userfunction.o(.text.Signal_OK), (84 bytes).
    Removing userfunction.o(.ARM.exidx.text.Signal_OK), (8 bytes).
    Removing userfunction.o(.text.Signal_NO), (72 bytes).
    Removing userfunction.o(.ARM.exidx.text.Signal_NO), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Send_To_Main), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.RotateM1_D14_Position), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.RotateM2_D13_Position), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Rotate_M1_CW), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Rotate_M1_CCW), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Rotate_M2_CW), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Rotate_M2_CCW), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Rotate_M3), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Rotate_M4), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Rotate_M5), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Rotate_M6), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Rotate_M6_Step), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Rotate_M6_Step_Safe), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Rotate_M7), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.GetEncoder_1_Angele), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.GetEncoder_2_Angele), (8 bytes).
    Removing userfunction.o(.text.LoadUp), (644 bytes).
    Removing userfunction.o(.ARM.exidx.text.LoadUp), (8 bytes).
    Removing userfunction.o(.text.Rotate_M6_Stage), (46 bytes).
    Removing userfunction.o(.ARM.exidx.text.Rotate_M6_Stage), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Rotate_M6_Max_Power), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Ready_Command), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Timer_Start), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Timer_Stop_And_Show), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Rotate_M3_Adaptive), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.M2_Hold_Position_Active), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Show_All_Motors_Status), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Return_All_Motors_Home), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Test_M1_Simple), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Test_All_Motors_Max_Speed), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Play_Note), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Play_Piano_Melody), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Show_About_Page), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Load_Embedded_Config), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Show_Current_Config), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Get_System_MS), (8 bytes).
    Removing userfunction.o(.text.Timer_Reset), (34 bytes).
    Removing userfunction.o(.ARM.exidx.text.Timer_Reset), (8 bytes).
    Removing userfunction.o(.text.Timer_Get_Elapsed_MS), (50 bytes).
    Removing userfunction.o(.ARM.exidx.text.Timer_Get_Elapsed_MS), (8 bytes).
    Removing userfunction.o(.text.M2_Hold_Position_Inactive), (36 bytes).
    Removing userfunction.o(.ARM.exidx.text.M2_Hold_Position_Inactive), (8 bytes).
    Removing userfunction.o(.text.M2_Hold_Position), (150 bytes).
    Removing userfunction.o(.ARM.exidx.text.M2_Hold_Position), (8 bytes).
    Removing userfunction.o(.text.Update_Motor_Stats), (164 bytes).
    Removing userfunction.o(.ARM.exidx.text.Update_Motor_Stats), (8 bytes).
    Removing userfunction.o(.text.Test_Motor_Stability), (454 bytes).
    Removing userfunction.o(.ARM.exidx.text.Test_Motor_Stability), (8 bytes).
    Removing userfunction.o(.ARM.exidx.text.Auto_Calibrate_All_Motors), (8 bytes).
    Removing userfunction.o(.text.Find_Optimal_Speed), (172 bytes).
    Removing userfunction.o(.ARM.exidx.text.Find_Optimal_Speed), (8 bytes).
    Removing userfunction.o(.text.Test_Motor_At_Speed), (466 bytes).
    Removing userfunction.o(.ARM.exidx.text.Test_Motor_At_Speed), (8 bytes).
    Removing userfunction.o(.text.Test_Motor_Reliability), (114 bytes).
    Removing userfunction.o(.ARM.exidx.text.Test_Motor_Reliability), (8 bytes).
    Removing userfunction.o(.text.Save_Motor_Speed), (230 bytes).
    Removing userfunction.o(.ARM.exidx.text.Save_Motor_Speed), (8 bytes).
    Removing userfunction.o(.text.Init_Motor_Health), (146 bytes).
    Removing userfunction.o(.ARM.exidx.text.Init_Motor_Health), (8 bytes).
    Removing userfunction.o(.text.Check_Maintenance_Needs), (312 bytes).
    Removing userfunction.o(.ARM.exidx.text.Check_Maintenance_Needs), (8 bytes).
    Removing userfunction.o(.text.Generate_Maintenance_Alert), (396 bytes).
    Removing userfunction.o(.ARM.exidx.text.Generate_Maintenance_Alert), (8 bytes).
    Removing userfunction.o(.bss.ADC_CH0_mV), (4 bytes).
    Removing userfunction.o(.bss.ADC_CH1_mV), (4 bytes).
    Removing userfunction.o(.bss.ADC_Result_CH0), (2 bytes).
    Removing userfunction.o(.bss.ADC_Result_CH1), (2 bytes).
    Removing userfunction.o(.bss.CurrentCharBuffer), (1 bytes).
    Removing userfunction.o(.bss.OUT_Current), (4 bytes).
    Removing userfunction.o(.bss.OUT_Current_Int), (2 bytes).
    Removing userfunction.o(.bss.OUT_Current_Frac), (2 bytes).
    Removing userfunction.o(.bss.OUT_Voltage), (4 bytes).
    Removing userfunction.o(.bss.OUT_Voltage_Int), (2 bytes).
    Removing userfunction.o(.bss.OUT_Voltage_Frac), (2 bytes).
    Removing userfunction.o(.rodata.VREF_4095), (4 bytes).
    Removing userfunction.o(.bss.VoltageCharBuffer), (1 bytes).
    Removing userfunction.o(.rodata.EMBEDDED_CONFIG), (152 bytes).
    Removing userfunction.o(.bss.motor_health), (192 bytes).
    Removing json_parser.o(.text), (0 bytes).
    Removing json_parser.o(.ARM.exidx.text.JSON_StringToCommand), (8 bytes).
    Removing json_parser.o(.ARM.exidx.text.simple_strcmp), (8 bytes).
    Removing json_parser.o(.text.JSON_GetCommandName), (254 bytes).
    Removing json_parser.o(.ARM.exidx.text.JSON_GetCommandName), (8 bytes).
    Removing json_parser.o(.ARM.exidx.text.JSON_GetStringValue), (8 bytes).
    Removing json_parser.o(.ARM.exidx.text.simple_strlen), (8 bytes).
    Removing json_parser.o(.ARM.exidx.text.JSON_GetIntValue), (8 bytes).
    Removing json_parser.o(.ARM.exidx.text.JSON_Parse), (8 bytes).
    Removing json_parser.o(.ARM.exidx.text.JSON_ExecuteCommand), (8 bytes).
    Removing io_gpio.o(.text), (0 bytes).
    Removing io_gpio.o(.ARM.exidx.text.SetupGpioIO), (8 bytes).
    Removing rcc.o(.text), (0 bytes).
    Removing rcc.o(.ARM.exidx.text.RCCInit), (8 bytes).
    Removing timers.o(.text), (0 bytes).
    Removing timers.o(.ARM.exidx.text.TIM2_IRQHandler), (8 bytes).
    Removing timers.o(.ARM.exidx.text.SetupTimers), (8 bytes).
    Removing timers.o(.ARM.exidx.text.TIM4_IRQHandler), (8 bytes).
    Removing i2c.o(.text), (0 bytes).
    Removing i2c.o(.ARM.exidx.text.SetUp_I2C1), (8 bytes).
    Removing i2c.o(.text.I2C1_Start), (38 bytes).
    Removing i2c.o(.ARM.exidx.text.I2C1_Start), (8 bytes).
    Removing i2c.o(.text.I2C1_AddrSend_Transmit), (66 bytes).
    Removing i2c.o(.ARM.exidx.text.I2C1_AddrSend_Transmit), (8 bytes).
    Removing i2c.o(.text.I2C1_AddrSend_Receive), (66 bytes).
    Removing i2c.o(.ARM.exidx.text.I2C1_AddrSend_Receive), (8 bytes).
    Removing lcd.o(.text), (0 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_Send_Command), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_Send_4BitCmd), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_Send_Data), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_Setup), (8 bytes).
    Removing lcd.o(.text.Test_1), (2 bytes).
    Removing lcd.o(.ARM.exidx.text.Test_1), (8 bytes).
    Removing lcd.o(.text.LCD_CursorGoToLeft), (58 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_CursorGoToLeft), (8 bytes).
    Removing lcd.o(.text.LCD_CursorGoToRight), (60 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_CursorGoToRight), (8 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_SendString), (8 bytes).
    Removing lcd.o(.text.LCD_Set_CGRAM), (434 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_Set_CGRAM), (8 bytes).
    Removing lcd.o(.text.LCD_Write_CGRAM), (452 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_Write_CGRAM), (8 bytes).
    Removing lcd.o(.text.Current_To_LCD), (140 bytes).
    Removing lcd.o(.ARM.exidx.text.Current_To_LCD), (8 bytes).
    Removing lcd.o(.text.Voltage_To_LCD), (140 bytes).
    Removing lcd.o(.ARM.exidx.text.Voltage_To_LCD), (8 bytes).
    Removing lcd.o(.text.LCD_UserChars), (2 bytes).
    Removing lcd.o(.ARM.exidx.text.LCD_UserChars), (8 bytes).
    Removing lcd.o(.bss.LCD_Current), (7 bytes).
    Removing lcd.o(.bss.LCD_Voltage), (7 bytes).
    Removing lcd.o(.data.LCD_Char0), (8 bytes).
    Removing lcd.o(.data.LCD_Char1), (8 bytes).
    Removing lcd.o(.data.LCD_Char2), (8 bytes).
    Removing lcd.o(.data.LCD_Char3), (8 bytes).
    Removing lcd.o(.data.LCD_Char4), (8 bytes).
    Removing lcd.o(.data.Chars0), (5 bytes).
    Removing lcd.o(.data.Chars1), (5 bytes).
    Removing lcd.o(.data.Chars2), (5 bytes).
    Removing lcd.o(.data.Chars3), (5 bytes).
    Removing lcd.o(.data.Chars4), (5 bytes).
    Removing system_stm32f10x.o(.text), (0 bytes).
    Removing system_stm32f10x.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32f10x.o(.ARM.exidx.text.SetSysClock), (8 bytes).
    Removing system_stm32f10x.o(.text.SystemCoreClockUpdate), (290 bytes).
    Removing system_stm32f10x.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing system_stm32f10x.o(.ARM.exidx.text.SetSysClockTo72), (8 bytes).
    Removing system_stm32f10x.o(.data.SystemCoreClock), (4 bytes).
    Removing system_stm32f10x.o(.rodata.AHBPrescTable), (16 bytes).

204 unused section(s) (total 7947 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fcmp.s                          0x00000000   Number         0  fcmp.o ABSOLUTE
    ../fplib/fcmpi.s                         0x00000000   Number         0  fcmpi.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/feqf.s                          0x00000000   Number         0  feqf.o ABSOLUTE
    ../fplib/ffix.s                          0x00000000   Number         0  ffix.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fgeqf.s                         0x00000000   Number         0  fgeqf.o ABSOLUTE
    ../fplib/fleqf.s                         0x00000000   Number         0  fleqf.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fpinit_empty.s                  0x00000000   Number         0  fpinit_empty.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    I2C.c                                    0x00000000   Number         0  i2c.o ABSOLUTE
    IO_gpio.c                                0x00000000   Number         0  io_gpio.o ABSOLUTE
    RTE/Device/STM32F103ZE/startup_stm32f10x_hd.s 0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    Rcc.c                                    0x00000000   Number         0  rcc.o ABSOLUTE
    Timers.c                                 0x00000000   Number         0  timers.o ABSOLUTE
    UserFunction.c                           0x00000000   Number         0  userfunction.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    json_parser.c                            0x00000000   Number         0  json_parser.o ABSOLUTE
    lcd.c                                    0x00000000   Number         0  lcd.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    system_stm32f10x.c                       0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       92  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000194   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_null                           0x080001b0   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x080001b4   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x080001d0   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x080001d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x080001d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x080001d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000013          0x080001d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080001d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080001d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x080001d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x080001d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x080001d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x080001d2   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x080001d4   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080001d6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080001d6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x080001d6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x080001d6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x080001d6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x080001d6   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x080001d6   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x080001d8   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001d8   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001d8   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001de   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001de   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001e2   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001e2   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001ea   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001ec   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001ec   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001f0   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001f8   Section       64  startup_stm32f10x_hd.o(.text)
    .text                                    0x08000238   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x080002c2   Section        0  heapauxi.o(.text)
    .text                                    0x080002c8   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x0800032c   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000376   Section        0  exit.o(.text)
    .text                                    0x08000388   Section        8  libspace.o(.text)
    .text                                    0x08000390   Section        0  sys_exit.o(.text)
    .text                                    0x0800039c   Section        2  use_no_semi.o(.text)
    .text                                    0x0800039e   Section        0  indicate_semi.o(.text)
    [Anonymous Symbol]                       0x080003a0   Section        0  userfunction.o(.text.ADC1_Config)
    [Anonymous Symbol]                       0x08000454   Section        0  userfunction.o(.text.Auto_Calibrate_All_Motors)
    [Anonymous Symbol]                       0x08000508   Section        0  main.o(.text.ClearCmdBuffer)
    [Anonymous Symbol]                       0x08000540   Section        0  main.o(.text.Delay_mS)
    [Anonymous Symbol]                       0x08000598   Section        0  main.o(.text.Delay_uS)
    [Anonymous Symbol]                       0x080005f4   Section        0  userfunction.o(.text.GetEncoder_1_Angele)
    [Anonymous Symbol]                       0x08000638   Section        0  userfunction.o(.text.GetEncoder_2_Angele)
    [Anonymous Symbol]                       0x0800067c   Section        0  userfunction.o(.text.Get_System_MS)
    [Anonymous Symbol]                       0x08000688   Section        0  json_parser.o(.text.JSON_ExecuteCommand)
    [Anonymous Symbol]                       0x080007f4   Section        0  json_parser.o(.text.JSON_GetIntValue)
    [Anonymous Symbol]                       0x080008ac   Section        0  json_parser.o(.text.JSON_GetStringValue)
    [Anonymous Symbol]                       0x08000a3c   Section        0  json_parser.o(.text.JSON_Parse)
    [Anonymous Symbol]                       0x08000b8c   Section        0  json_parser.o(.text.JSON_StringToCommand)
    [Anonymous Symbol]                       0x08000d54   Section        0  lcd.o(.text.LCD_SendString)
    [Anonymous Symbol]                       0x08000d94   Section        0  lcd.o(.text.LCD_Send_4BitCmd)
    [Anonymous Symbol]                       0x08000e9c   Section        0  lcd.o(.text.LCD_Send_Command)
    [Anonymous Symbol]                       0x08001038   Section        0  lcd.o(.text.LCD_Send_Data)
    [Anonymous Symbol]                       0x080011d4   Section        0  lcd.o(.text.LCD_Setup)
    [Anonymous Symbol]                       0x08001254   Section        0  userfunction.o(.text.Load_Embedded_Config)
    [Anonymous Symbol]                       0x08001298   Section        0  userfunction.o(.text.M2_Hold_Position_Active)
    NVIC_EnableIRQ                           0x080012bd   Thumb Code    10  main.o(.text.NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x080012bc   Section        0  main.o(.text.NVIC_EnableIRQ)
    NVIC_SetPriority                         0x080012c9   Thumb Code    12  main.o(.text.NVIC_SetPriority)
    [Anonymous Symbol]                       0x080012c8   Section        0  main.o(.text.NVIC_SetPriority)
    [Anonymous Symbol]                       0x080012d4   Section        0  userfunction.o(.text.Play_Note)
    [Anonymous Symbol]                       0x080013ec   Section        0  userfunction.o(.text.Play_Piano_Melody)
    [Anonymous Symbol]                       0x080015a8   Section        0  rcc.o(.text.RCCInit)
    [Anonymous Symbol]                       0x08001670   Section        0  userfunction.o(.text.Ready_Command)
    [Anonymous Symbol]                       0x080017a4   Section        0  userfunction.o(.text.Return_All_Motors_Home)
    [Anonymous Symbol]                       0x08001890   Section        0  userfunction.o(.text.RotateM1_D14_Position)
    [Anonymous Symbol]                       0x08001a40   Section        0  userfunction.o(.text.RotateM2_D13_Position)
    [Anonymous Symbol]                       0x08001be0   Section        0  userfunction.o(.text.Rotate_M1_CCW)
    [Anonymous Symbol]                       0x08001de0   Section        0  userfunction.o(.text.Rotate_M1_CW)
    [Anonymous Symbol]                       0x08001fe0   Section        0  userfunction.o(.text.Rotate_M2_CCW)
    [Anonymous Symbol]                       0x08002158   Section        0  userfunction.o(.text.Rotate_M2_CW)
    [Anonymous Symbol]                       0x080022c8   Section        0  userfunction.o(.text.Rotate_M3)
    [Anonymous Symbol]                       0x080024e4   Section        0  userfunction.o(.text.Rotate_M3_Adaptive)
    [Anonymous Symbol]                       0x0800264c   Section        0  userfunction.o(.text.Rotate_M4)
    [Anonymous Symbol]                       0x08002864   Section        0  userfunction.o(.text.Rotate_M5)
    [Anonymous Symbol]                       0x08002a80   Section        0  userfunction.o(.text.Rotate_M6)
    [Anonymous Symbol]                       0x08002c74   Section        0  userfunction.o(.text.Rotate_M6_Max_Power)
    [Anonymous Symbol]                       0x08002dac   Section        0  userfunction.o(.text.Rotate_M6_Step)
    [Anonymous Symbol]                       0x08002e78   Section        0  userfunction.o(.text.Rotate_M6_Step_Safe)
    [Anonymous Symbol]                       0x08003074   Section        0  userfunction.o(.text.Rotate_M7)
    [Anonymous Symbol]                       0x08003240   Section        0  main.o(.text.SaveReceivedCommand)
    [Anonymous Symbol]                       0x08003280   Section        0  userfunction.o(.text.Send_To_Main)
    SetSysClock                              0x080032cd   Thumb Code     8  system_stm32f10x.o(.text.SetSysClock)
    [Anonymous Symbol]                       0x080032cc   Section        0  system_stm32f10x.o(.text.SetSysClock)
    SetSysClockTo72                          0x080032d5   Thumb Code   290  system_stm32f10x.o(.text.SetSysClockTo72)
    [Anonymous Symbol]                       0x080032d4   Section        0  system_stm32f10x.o(.text.SetSysClockTo72)
    [Anonymous Symbol]                       0x080033f8   Section        0  i2c.o(.text.SetUp_I2C1)
    [Anonymous Symbol]                       0x08003450   Section        0  io_gpio.o(.text.SetupGpioIO)
    [Anonymous Symbol]                       0x08003ad8   Section        0  timers.o(.text.SetupTimers)
    [Anonymous Symbol]                       0x08003b64   Section        0  userfunction.o(.text.Show_About_Page)
    [Anonymous Symbol]                       0x08003d94   Section        0  userfunction.o(.text.Show_All_Motors_Status)
    [Anonymous Symbol]                       0x08003e50   Section        0  userfunction.o(.text.Show_Current_Config)
    [Anonymous Symbol]                       0x08003f34   Section        0  userfunction.o(.text.Signal_3p1D)
    [Anonymous Symbol]                       0x08003fb0   Section        0  system_stm32f10x.o(.text.SystemInit)
    [Anonymous Symbol]                       0x08004018   Section        0  timers.o(.text.TIM2_IRQHandler)
    [Anonymous Symbol]                       0x08004028   Section        0  timers.o(.text.TIM4_IRQHandler)
    [Anonymous Symbol]                       0x0800404c   Section        0  userfunction.o(.text.Test_All_Motors_Max_Speed)
    [Anonymous Symbol]                       0x08004728   Section        0  userfunction.o(.text.Test_M1_Simple)
    [Anonymous Symbol]                       0x08004874   Section        0  userfunction.o(.text.Timer_Start)
    [Anonymous Symbol]                       0x080048a8   Section        0  userfunction.o(.text.Timer_Stop_And_Show)
    [Anonymous Symbol]                       0x08004a50   Section        0  main.o(.text.USART1_IRQHandler)
    [Anonymous Symbol]                       0x08004b50   Section        0  main.o(.text.USART2_IRQHandler)
    __enable_irq                             0x08004b71   Thumb Code     2  main.o(.text.__enable_irq)
    [Anonymous Symbol]                       0x08004b70   Section        0  main.o(.text.__enable_irq)
    [Anonymous Symbol]                       0x08004b74   Section        0  main.o(.text.main)
    simple_strcmp                            0x08005f8d   Thumb Code   108  json_parser.o(.text.simple_strcmp)
    [Anonymous Symbol]                       0x08005f8c   Section        0  json_parser.o(.text.simple_strcmp)
    simple_strlen                            0x08005ff9   Thumb Code    72  json_parser.o(.text.simple_strlen)
    [Anonymous Symbol]                       0x08005ff8   Section        0  json_parser.o(.text.simple_strlen)
    .L__const.Play_Piano_Melody.notes        0x08006040   Data          24  userfunction.o(.rodata..L__const.Play_Piano_Melody.notes)
    .L.str.13                                0x08006058   Data          16  main.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x08006058   Section        0  main.o(.rodata.str1.1)
    .L.str.46                                0x08006068   Data          21  main.o(.rodata.str1.1)
    .L.str.48                                0x0800607d   Data          21  main.o(.rodata.str1.1)
    .L.str.50                                0x08006092   Data          21  main.o(.rodata.str1.1)
    .L.str.52                                0x080060a7   Data          21  main.o(.rodata.str1.1)
    .L.str.45                                0x080060bc   Data          21  main.o(.rodata.str1.1)
    .L.str.47                                0x080060d1   Data          21  main.o(.rodata.str1.1)
    .L.str.49                                0x080060e6   Data          21  main.o(.rodata.str1.1)
    .L.str.51                                0x080060fb   Data          21  main.o(.rodata.str1.1)
    .L.str.5                                 0x08006110   Data          21  main.o(.rodata.str1.1)
    .L.str.14                                0x08006125   Data          21  main.o(.rodata.str1.1)
    .L.str.44                                0x0800613a   Data          21  main.o(.rodata.str1.1)
    .L.str.7                                 0x0800614f   Data          21  main.o(.rodata.str1.1)
    .L.str.12                                0x08006164   Data          24  main.o(.rodata.str1.1)
    .L.str.8                                 0x0800617c   Data          24  main.o(.rodata.str1.1)
    .L.str.10                                0x08006194   Data          25  main.o(.rodata.str1.1)
    .L.str.9                                 0x080061ad   Data          25  main.o(.rodata.str1.1)
    .L.str.11                                0x080061c6   Data          25  main.o(.rodata.str1.1)
    .L.str.42                                0x080061df   Data          21  main.o(.rodata.str1.1)
    .L.str.43                                0x080061f4   Data          22  main.o(.rodata.str1.1)
    .L.str.6                                 0x0800620a   Data          21  main.o(.rodata.str1.1)
    .L.str.53                                0x0800621f   Data          21  main.o(.rodata.str1.1)
    .L.str.1                                 0x08006234   Data          16  main.o(.rodata.str1.1)
    .L.str.4                                 0x08006244   Data          18  main.o(.rodata.str1.1)
    .L.str.2                                 0x08006256   Data          25  main.o(.rodata.str1.1)
    .L.str.39                                0x0800626f   Data          21  main.o(.rodata.str1.1)
    .L.str                                   0x08006284   Data          20  main.o(.rodata.str1.1)
    .L.str.97                                0x08006284   Data          20  main.o(.rodata.str1.1)
    .L.str.3                                 0x08006298   Data           4  main.o(.rodata.str1.1)
    .L.str.25                                0x0800629c   Data          21  main.o(.rodata.str1.1)
    .L.str.24                                0x080062b1   Data          21  main.o(.rodata.str1.1)
    .L.str.16                                0x080062c6   Data          21  main.o(.rodata.str1.1)
    .L.str.18                                0x080062db   Data          21  main.o(.rodata.str1.1)
    .L.str.20                                0x080062f0   Data          21  main.o(.rodata.str1.1)
    .L.str.22                                0x08006305   Data          21  main.o(.rodata.str1.1)
    .L.str.26                                0x0800631a   Data          21  main.o(.rodata.str1.1)
    .L.str.15                                0x0800632f   Data          21  main.o(.rodata.str1.1)
    .L.str.17                                0x08006344   Data          21  main.o(.rodata.str1.1)
    .L.str.19                                0x08006359   Data          21  main.o(.rodata.str1.1)
    .L.str.21                                0x0800636e   Data          21  main.o(.rodata.str1.1)
    .L.str.23                                0x08006383   Data          21  main.o(.rodata.str1.1)
    .L.str.41                                0x08006398   Data          22  main.o(.rodata.str1.1)
    .L.str.33                                0x080063ae   Data          22  main.o(.rodata.str1.1)
    .L.str.40                                0x080063c4   Data          22  main.o(.rodata.str1.1)
    .L.str.31                                0x080063da   Data          19  main.o(.rodata.str1.1)
    .L.str.36                                0x080063ed   Data          21  main.o(.rodata.str1.1)
    .L.str.73                                0x080063ed   Data          21  main.o(.rodata.str1.1)
    .L.str.28                                0x08006402   Data          21  main.o(.rodata.str1.1)
    .L.str.34                                0x08006417   Data          20  main.o(.rodata.str1.1)
    .L.str.38                                0x0800642b   Data          22  main.o(.rodata.str1.1)
    .L.str.29                                0x08006441   Data          20  main.o(.rodata.str1.1)
    .L.str.30                                0x08006455   Data          20  main.o(.rodata.str1.1)
    .L.str.37                                0x08006469   Data          22  main.o(.rodata.str1.1)
    .L.str.27                                0x0800647f   Data          21  main.o(.rodata.str1.1)
    .L.str.35                                0x08006494   Data          23  main.o(.rodata.str1.1)
    .L.str.32                                0x080064ab   Data          21  main.o(.rodata.str1.1)
    .L.str                                   0x080064c0   Data           3  userfunction.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x080064c0   Section        0  userfunction.o(.rodata.str1.1)
    .L.str.1                                 0x080064c3   Data           5  userfunction.o(.rodata.str1.1)
    .L.str.65                                0x080064d5   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.15                                0x080064ea   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.19                                0x080064ff   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.23                                0x08006514   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.83                                0x08006529   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.85                                0x08006553   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.118                               0x0800657d   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.26                                0x08006592   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.113                               0x080065a7   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.110                               0x080065bc   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.69                                0x080065d1   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.67                                0x080065e6   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.91                                0x08006610   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.84                                0x08006625   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.68                                0x0800663a   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.63                                0x0800664f   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.90                                0x08006679   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.70                                0x0800668e   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.95                                0x080066a3   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.14                                0x080066b8   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.13                                0x080066cd   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.22                                0x080066e2   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.21                                0x080066f7   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.17                                0x0800670c   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.18                                0x08006721   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.31                                0x08006736   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.4                                 0x08006760   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.7                                 0x08006775   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.16                                0x0800678a   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.20                                0x0800679f   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.24                                0x080067b4   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.27                                0x080067c9   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.39                                0x080067d5   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.6                                 0x080067de   Data          23  userfunction.o(.rodata.str1.1)
    .L.str.40                                0x080067ea   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.3                                 0x080067f5   Data          23  userfunction.o(.rodata.str1.1)
    .L.str.41                                0x080067ff   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.9                                 0x0800680c   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.42                                0x08006814   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.109                               0x08006821   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.43                                0x08006829   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.101                               0x08006836   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.44                                0x0800683e   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.96                                0x0800684b   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.45                                0x08006853   Data          18  userfunction.o(.rodata.str1.1)
    .L.str.87                                0x08006860   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.46                                0x08006865   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.103                               0x08006875   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.47                                0x0800687a   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.5                                 0x0800688a   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.48                                0x0800688f   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.2                                 0x0800689f   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.49                                0x080068a4   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.34                                0x080068b4   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.50                                0x080068b9   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.37                                0x080068c9   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.51                                0x080068ce   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.28                                0x080068de   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.52                                0x080068e3   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.60                                0x080068f3   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.53                                0x080068f8   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.54                                0x0800690d   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.98                                0x0800691d   Data          19  userfunction.o(.rodata.str1.1)
    .L.str.107                               0x08006930   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.29                                0x0800695a   Data          20  userfunction.o(.rodata.str1.1)
    .L.str.64                                0x0800696e   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.58                                0x08006983   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.11                                0x08006998   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.33                                0x080069ad   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.108                               0x080069c2   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.59                                0x080069d7   Data          22  userfunction.o(.rodata.str1.1)
    .L.str.102                               0x080069ed   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.117                               0x08006a02   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.106                               0x08006a17   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.99                                0x08006a2c   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.32                                0x08006a41   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.55                                0x08006a56   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.61                                0x08006a95   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.72                                0x08006abc   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.81                                0x08006ad1   Data          24  userfunction.o(.rodata.str1.1)
    .L.str.10                                0x08006afd   Data          22  userfunction.o(.rodata.str1.1)
    .L.str.12                                0x08006b13   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.119                               0x08006b28   Data          22  userfunction.o(.rodata.str1.1)
    .L.str.66                                0x08006b3e   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.115                               0x08006b53   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.104                               0x08006b68   Data          22  userfunction.o(.rodata.str1.1)
    .L.str.36                                0x08006bd2   Data          20  userfunction.o(.rodata.str1.1)
    .L.str.116                               0x08006be6   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.88                                0x08006bfb   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.92                                0x08006c10   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.25                                0x08006c25   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.56                                0x08006c3a   Data          23  userfunction.o(.rodata.str1.1)
    .L.str.71                                0x08006c51   Data          22  userfunction.o(.rodata.str1.1)
    .L.str.62                                0x08006c67   Data          20  userfunction.o(.rodata.str1.1)
    .L.str.114                               0x08006c7b   Data          25  userfunction.o(.rodata.str1.1)
    .L.str.112                               0x08006c94   Data          22  userfunction.o(.rodata.str1.1)
    .L.str.8                                 0x08006cb2   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.57                                0x08006cc7   Data          22  userfunction.o(.rodata.str1.1)
    .L.str.79                                0x08006cdd   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.38                                0x08006cf2   Data          22  userfunction.o(.rodata.str1.1)
    .L.str.35                                0x08006d08   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.94                                0x08006d1d   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.74                                0x08006d32   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.75                                0x08006d47   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.76                                0x08006d5c   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.77                                0x08006d71   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.78                                0x08006d86   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.80                                0x08006d9b   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.30                                0x08006db0   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.89                                0x08006dc5   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.111                               0x08006dc8   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.93                                0x08006dda   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.100                               0x08006def   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.105                               0x08006e04   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.82                                0x08006e19   Data          25  userfunction.o(.rodata.str1.1)
    .L.str.86                                0x08006e32   Data          26  userfunction.o(.rodata.str1.1)
    .L.str.39                                0x08006e4c   Data          21  json_parser.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x08006e4c   Section        0  json_parser.o(.rodata.str1.1)
    .L.str.35                                0x08006e61   Data          21  json_parser.o(.rodata.str1.1)
    .L.str.36                                0x08006e76   Data          21  json_parser.o(.rodata.str1.1)
    .L.str.120                               0x08006e8b   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.37                                0x08006e8b   Data          21  json_parser.o(.rodata.str1.1)
    .L.str.121                               0x08006ea0   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.38                                0x08006ea0   Data          21  json_parser.o(.rodata.str1.1)
    .L.str.122                               0x08006eb5   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.41                                0x08006eb5   Data          21  json_parser.o(.rodata.str1.1)
    .L.str.123                               0x08006eca   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.34                                0x08006eca   Data          21  json_parser.o(.rodata.str1.1)
    .L.str.124                               0x08006edf   Data          20  userfunction.o(.rodata.str1.1)
    .L.str.33                                0x08006edf   Data          21  json_parser.o(.rodata.str1.1)
    .L.str.17                                0x08006ee4   Data           8  json_parser.o(.rodata.str1.1)
    .L.str.125                               0x08006ef3   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.29                                0x08006ef4   Data          21  json_parser.o(.rodata.str1.1)
    .L.str.126                               0x08006f08   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.31                                0x08006f09   Data          21  json_parser.o(.rodata.str1.1)
    .L.str.127                               0x08006f1d   Data          21  userfunction.o(.rodata.str1.1)
    .L.str.30                                0x08006f1e   Data          21  json_parser.o(.rodata.str1.1)
    .L.str.5                                 0x08006f33   Data           8  json_parser.o(.rodata.str1.1)
    .L.str.40                                0x08006f3b   Data          21  json_parser.o(.rodata.str1.1)
    .L.str.16                                0x08006f50   Data           3  json_parser.o(.rodata.str1.1)
    .L.str.8                                 0x08006f53   Data          10  json_parser.o(.rodata.str1.1)
    .L.str.22                                0x08006f57   Data           6  json_parser.o(.rodata.str1.1)
    .L.str.18                                0x08006f5d   Data           4  json_parser.o(.rodata.str1.1)
    .L.str.32                                0x08006f61   Data          21  json_parser.o(.rodata.str1.1)
    .L.str.26                                0x08006f76   Data           6  json_parser.o(.rodata.str1.1)
    .L.str.4                                 0x08006f7c   Data           5  json_parser.o(.rodata.str1.1)
    .L.str.15                                0x08006f81   Data          15  json_parser.o(.rodata.str1.1)
    .L.str.9                                 0x08006f90   Data          12  json_parser.o(.rodata.str1.1)
    .L.str.10                                0x08006f9c   Data          12  json_parser.o(.rodata.str1.1)
    .L.str.24                                0x08006fa8   Data          12  json_parser.o(.rodata.str1.1)
    .L.str.28                                0x08006fb4   Data           9  json_parser.o(.rodata.str1.1)
    .L.str.27                                0x08006fbd   Data          11  json_parser.o(.rodata.str1.1)
    .L.str.12                                0x08006fc8   Data           9  json_parser.o(.rodata.str1.1)
    .L.str.6                                 0x08006fd1   Data           9  json_parser.o(.rodata.str1.1)
    .L.str.13                                0x08006fda   Data           4  json_parser.o(.rodata.str1.1)
    .L.str.25                                0x08006fde   Data           9  json_parser.o(.rodata.str1.1)
    .L.str.20                                0x08006fe7   Data          10  json_parser.o(.rodata.str1.1)
    .L.str.14                                0x08006ff1   Data          13  json_parser.o(.rodata.str1.1)
    .L.str.7                                 0x08007006   Data          11  json_parser.o(.rodata.str1.1)
    .L.str.19                                0x0800700b   Data           6  json_parser.o(.rodata.str1.1)
    .L.str.21                                0x08007011   Data           6  json_parser.o(.rodata.str1.1)
    .L.str.11                                0x08007017   Data          11  json_parser.o(.rodata.str1.1)
    .L.str                                   0x08007022   Data           6  json_parser.o(.rodata.str1.1)
    .L.str.2                                 0x08007028   Data           6  json_parser.o(.rodata.str1.1)
    .L.str.23                                0x0800702e   Data          11  json_parser.o(.rodata.str1.1)
    .L.str.1                                 0x08007039   Data           6  json_parser.o(.rodata.str1.1)
    .L.str.3                                 0x0800703f   Data          12  json_parser.o(.rodata.str1.1)
    .bss                                     0x20000020   Section       96  libspace.o(.bss)
    Rotate_M6_Step_Safe.step_counter         0x2000008a   Data           2  userfunction.o(.bss.Rotate_M6_Step_Safe.step_counter)
    [Anonymous Symbol]                       0x2000008a   Section        0  userfunction.o(.bss.Rotate_M6_Step_Safe.step_counter)
    Heap_Mem                                 0x200001c8   Data         512  startup_stm32f10x_hd.o(HEAP)
    HEAP                                     0x200001c8   Section      512  startup_stm32f10x_hd.o(HEAP)
    Stack_Mem                                0x200003c8   Data        1024  startup_stm32f10x_hd.o(STACK)
    STACK                                    0x200003c8   Section     1024  startup_stm32f10x_hd.o(STACK)
    __initial_sp                             0x200007c8   Data           0  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __fp_init_empty                          0x00000000   Number         0  fpinit_empty.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    84  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x08000143   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000195   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_null                       0x080001b1   Thumb Code     2  __scatter.o(!!handler_null)
    __scatterload_zeroinit                   0x080001b5   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x080001d1   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_atexit_1                   0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_cpp_1                      0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_fp_1                       0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_heap_1                     0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_1                 0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_1               0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_preinit_1                  0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_rand_1                     0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_relocate_pie_1             0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_return                     0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_signal_1                   0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_1                    0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_init_user_alloc_1               0x080001d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_shutdown                        0x080001d5   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080001d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080001d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x080001d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x080001d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x080001d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x080001d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x080001d7   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x080001d9   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001d9   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001d9   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001df   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001df   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001e3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001e3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001eb   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001ed   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001ed   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001f1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080001f9   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    NMI_Handler                              0x08000201   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    HardFault_Handler                        0x08000203   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    MemManage_Handler                        0x08000205   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    BusFault_Handler                         0x08000207   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    UsageFault_Handler                       0x08000209   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    SVC_Handler                              0x0800020b   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    DebugMon_Handler                         0x0800020d   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    PendSV_Handler                           0x0800020f   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    SysTick_Handler                          0x08000211   Thumb Code     2  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x08000213   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __user_initial_stackheap                 0x08000215   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __aeabi_memcpy                           0x08000239   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x08000239   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x0800029f   Thumb Code     0  rt_memcpy_v6.o(.text)
    __use_two_region_memory                  0x080002c3   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080002c5   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080002c7   Thumb Code     2  heapauxi.o(.text)
    __aeabi_memcpy4                          0x080002c9   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x080002c9   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x080002c9   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000311   Thumb Code     0  rt_memcpy_w.o(.text)
    __user_setup_stackheap                   0x0800032d   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000377   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x08000389   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000389   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000389   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x08000391   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x0800039d   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0800039d   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x0800039f   Thumb Code     0  indicate_semi.o(.text)
    ADC1_Config                              0x080003a1   Thumb Code   178  userfunction.o(.text.ADC1_Config)
    Auto_Calibrate_All_Motors                0x08000455   Thumb Code   180  userfunction.o(.text.Auto_Calibrate_All_Motors)
    ClearCmdBuffer                           0x08000509   Thumb Code    54  main.o(.text.ClearCmdBuffer)
    Delay_mS                                 0x08000541   Thumb Code    86  main.o(.text.Delay_mS)
    Delay_uS                                 0x08000599   Thumb Code    92  main.o(.text.Delay_uS)
    GetEncoder_1_Angele                      0x080005f5   Thumb Code    66  userfunction.o(.text.GetEncoder_1_Angele)
    GetEncoder_2_Angele                      0x08000639   Thumb Code    66  userfunction.o(.text.GetEncoder_2_Angele)
    Get_System_MS                            0x0800067d   Thumb Code    12  userfunction.o(.text.Get_System_MS)
    JSON_ExecuteCommand                      0x08000689   Thumb Code   364  json_parser.o(.text.JSON_ExecuteCommand)
    JSON_GetIntValue                         0x080007f5   Thumb Code   182  json_parser.o(.text.JSON_GetIntValue)
    JSON_GetStringValue                      0x080008ad   Thumb Code   398  json_parser.o(.text.JSON_GetStringValue)
    JSON_Parse                               0x08000a3d   Thumb Code   336  json_parser.o(.text.JSON_Parse)
    JSON_StringToCommand                     0x08000b8d   Thumb Code   456  json_parser.o(.text.JSON_StringToCommand)
    LCD_SendString                           0x08000d55   Thumb Code    62  lcd.o(.text.LCD_SendString)
    LCD_Send_4BitCmd                         0x08000d95   Thumb Code   262  lcd.o(.text.LCD_Send_4BitCmd)
    LCD_Send_Command                         0x08000e9d   Thumb Code   410  lcd.o(.text.LCD_Send_Command)
    LCD_Send_Data                            0x08001039   Thumb Code   410  lcd.o(.text.LCD_Send_Data)
    LCD_Setup                                0x080011d5   Thumb Code   126  lcd.o(.text.LCD_Setup)
    Load_Embedded_Config                     0x08001255   Thumb Code    66  userfunction.o(.text.Load_Embedded_Config)
    M2_Hold_Position_Active                  0x08001299   Thumb Code    36  userfunction.o(.text.M2_Hold_Position_Active)
    Play_Note                                0x080012d5   Thumb Code   278  userfunction.o(.text.Play_Note)
    Play_Piano_Melody                        0x080013ed   Thumb Code   444  userfunction.o(.text.Play_Piano_Melody)
    RCCInit                                  0x080015a9   Thumb Code   198  rcc.o(.text.RCCInit)
    Ready_Command                            0x08001671   Thumb Code   308  userfunction.o(.text.Ready_Command)
    Return_All_Motors_Home                   0x080017a5   Thumb Code   234  userfunction.o(.text.Return_All_Motors_Home)
    RotateM1_D14_Position                    0x08001891   Thumb Code   432  userfunction.o(.text.RotateM1_D14_Position)
    RotateM2_D13_Position                    0x08001a41   Thumb Code   416  userfunction.o(.text.RotateM2_D13_Position)
    Rotate_M1_CCW                            0x08001be1   Thumb Code   510  userfunction.o(.text.Rotate_M1_CCW)
    Rotate_M1_CW                             0x08001de1   Thumb Code   510  userfunction.o(.text.Rotate_M1_CW)
    Rotate_M2_CCW                            0x08001fe1   Thumb Code   374  userfunction.o(.text.Rotate_M2_CCW)
    Rotate_M2_CW                             0x08002159   Thumb Code   366  userfunction.o(.text.Rotate_M2_CW)
    Rotate_M3                                0x080022c9   Thumb Code   538  userfunction.o(.text.Rotate_M3)
    Rotate_M3_Adaptive                       0x080024e5   Thumb Code   360  userfunction.o(.text.Rotate_M3_Adaptive)
    Rotate_M4                                0x0800264d   Thumb Code   536  userfunction.o(.text.Rotate_M4)
    Rotate_M5                                0x08002865   Thumb Code   540  userfunction.o(.text.Rotate_M5)
    Rotate_M6                                0x08002a81   Thumb Code   500  userfunction.o(.text.Rotate_M6)
    Rotate_M6_Max_Power                      0x08002c75   Thumb Code   312  userfunction.o(.text.Rotate_M6_Max_Power)
    Rotate_M6_Step                           0x08002dad   Thumb Code   204  userfunction.o(.text.Rotate_M6_Step)
    Rotate_M6_Step_Safe                      0x08002e79   Thumb Code   506  userfunction.o(.text.Rotate_M6_Step_Safe)
    Rotate_M7                                0x08003075   Thumb Code   458  userfunction.o(.text.Rotate_M7)
    SaveReceivedCommand                      0x08003241   Thumb Code    62  main.o(.text.SaveReceivedCommand)
    Send_To_Main                             0x08003281   Thumb Code    76  userfunction.o(.text.Send_To_Main)
    SetUp_I2C1                               0x080033f9   Thumb Code    88  i2c.o(.text.SetUp_I2C1)
    SetupGpioIO                              0x08003451   Thumb Code  1670  io_gpio.o(.text.SetupGpioIO)
    SetupTimers                              0x08003ad9   Thumb Code   138  timers.o(.text.SetupTimers)
    Show_About_Page                          0x08003b65   Thumb Code   560  userfunction.o(.text.Show_About_Page)
    Show_All_Motors_Status                   0x08003d95   Thumb Code   186  userfunction.o(.text.Show_All_Motors_Status)
    Show_Current_Config                      0x08003e51   Thumb Code   226  userfunction.o(.text.Show_Current_Config)
    Signal_3p1D                              0x08003f35   Thumb Code   124  userfunction.o(.text.Signal_3p1D)
    SystemInit                               0x08003fb1   Thumb Code   102  system_stm32f10x.o(.text.SystemInit)
    TIM2_IRQHandler                          0x08004019   Thumb Code    16  timers.o(.text.TIM2_IRQHandler)
    TIM4_IRQHandler                          0x08004029   Thumb Code    34  timers.o(.text.TIM4_IRQHandler)
    Test_All_Motors_Max_Speed                0x0800404d   Thumb Code  1756  userfunction.o(.text.Test_All_Motors_Max_Speed)
    Test_M1_Simple                           0x08004729   Thumb Code   330  userfunction.o(.text.Test_M1_Simple)
    Timer_Start                              0x08004875   Thumb Code    50  userfunction.o(.text.Timer_Start)
    Timer_Stop_And_Show                      0x080048a9   Thumb Code   422  userfunction.o(.text.Timer_Stop_And_Show)
    USART1_IRQHandler                        0x08004a51   Thumb Code   256  main.o(.text.USART1_IRQHandler)
    USART2_IRQHandler                        0x08004b51   Thumb Code    32  main.o(.text.USART2_IRQHandler)
    main                                     0x08004b75   Thumb Code  5142  main.o(.text.main)
    Region$$Table$$Base                      0x0800704c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800706c   Number         0  anon$$obj.o(Region$$Table)
    M1_PulseWidth                            0x20000000   Data           2  main.o(.data.M1_PulseWidth)
    M1_StepDelay                             0x20000002   Data           2  main.o(.data.M1_StepDelay)
    M2_ExtraDelay_CCW                        0x20000004   Data           2  main.o(.data.M2_ExtraDelay_CCW)
    M2_PulseWidth_CCW                        0x20000006   Data           2  main.o(.data.M2_PulseWidth_CCW)
    M2_StepDelay_CCW                         0x20000008   Data           2  main.o(.data.M2_StepDelay_CCW)
    M2_StepDelay_CW                          0x2000000a   Data           2  main.o(.data.M2_StepDelay_CW)
    M3_PulseWidth_uS                         0x2000000c   Data           2  main.o(.data.M3_PulseWidth_uS)
    M3_StepDelay_uS                          0x2000000e   Data           2  main.o(.data.M3_StepDelay_uS)
    M4_PulseWidth_uS                         0x20000010   Data           2  main.o(.data.M4_PulseWidth_uS)
    M4_StepDelay_uS                          0x20000012   Data           2  main.o(.data.M4_StepDelay_uS)
    M5_PulseWidth_uS                         0x20000014   Data           2  main.o(.data.M5_PulseWidth_uS)
    M5_StepDelay_uS                          0x20000016   Data           2  main.o(.data.M5_StepDelay_uS)
    M6_PulseWidth                            0x20000018   Data           2  main.o(.data.M6_PulseWidth)
    M6_StepDelay                             0x2000001a   Data           2  main.o(.data.M6_StepDelay)
    __libspace_start                         0x20000020   Data          96  libspace.o(.bss)
    Encoders_Angele                          0x20000080   Data           2  main.o(.bss.Encoders_Angele)
    __temporary_stack_top$libspace           0x20000080   Data           0  libspace.o(.bss)
    M1_Angele                                0x20000082   Data           2  main.o(.bss.M1_Angele)
    M2_Angele                                0x20000084   Data           2  main.o(.bss.M2_Angele)
    M7_Error                                 0x20000086   Data           1  main.o(.bss.M7_Error)
    Rotate_Angele                            0x20000088   Data           2  main.o(.bss.Rotate_Angele)
    SensorPositionError                      0x2000008c   Data           1  main.o(.bss.SensorPositionError)
    g_system_ms_counter                      0x20000090   Data           4  userfunction.o(.bss.g_system_ms_counter)
    g_timer_current_ms                       0x20000094   Data           4  userfunction.o(.bss.g_timer_current_ms)
    g_timer_running                          0x20000098   Data           1  userfunction.o(.bss.g_timer_running)
    g_timer_start_ms                         0x2000009c   Data           4  userfunction.o(.bss.g_timer_start_ms)
    motor_stats                              0x200000a0   Data         160  userfunction.o(.bss.motor_stats)
    position_hold_active                     0x20000140   Data           1  userfunction.o(.bss.position_hold_active)
    projectile_number                        0x20000141   Data           1  main.o(.bss.projectile_number)
    u8_CmdBuffer_1                           0x20000142   Data          64  main.o(.bss.u8_CmdBuffer_1)
    u8_CmdIndex_1                            0x20000182   Data           1  main.o(.bss.u8_CmdIndex_1)
    u8_CmdNumber                             0x20000183   Data           1  main.o(.bss.u8_CmdNumber)
    u8_ReceivedCommand                       0x20000184   Data          64  main.o(.bss.u8_ReceivedCommand)
    u8_Uart1_Cmd                             0x200001c4   Data           1  main.o(.bss.u8_Uart1_Cmd)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00007090, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000706c, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          345    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000008   Code   RO          375  * !!!main             c_w.l(__main.o)
    0x08000138   0x08000138   0x0000005c   Code   RO          595    !!!scatter          c_w.l(__scatter.o)
    0x08000194   0x08000194   0x0000001a   Code   RO          599    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001ae   0x080001ae   0x00000002   PAD
    0x080001b0   0x080001b0   0x00000002   Code   RO          596    !!handler_null      c_w.l(__scatter.o)
    0x080001b2   0x080001b2   0x00000002   PAD
    0x080001b4   0x080001b4   0x0000001c   Code   RO          601    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001d0   0x080001d0   0x00000002   Code   RO          457    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO          464    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO          466    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO          468    .ARM.Collect$$libinit$$00000006  c_w.l(libinit2.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO          471    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO          473    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO          475    .ARM.Collect$$libinit$$00000010  c_w.l(libinit2.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO          478    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO          480    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO          482    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO          484    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO          486    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO          488    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO          490    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO          492    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO          494    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO          496    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO          498    .ARM.Collect$$libinit$$00000027  c_w.l(libinit2.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO          502    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO          504    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO          506    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001d2   0x080001d2   0x00000000   Code   RO          508    .ARM.Collect$$libinit$$00000034  c_w.l(libinit2.o)
    0x080001d2   0x080001d2   0x00000002   Code   RO          509    .ARM.Collect$$libinit$$00000035  c_w.l(libinit2.o)
    0x080001d4   0x080001d4   0x00000002   Code   RO          531    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001d6   0x080001d6   0x00000000   Code   RO          546    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080001d6   0x080001d6   0x00000000   Code   RO          548    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080001d6   0x080001d6   0x00000000   Code   RO          551    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x080001d6   0x080001d6   0x00000000   Code   RO          554    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x080001d6   0x080001d6   0x00000000   Code   RO          556    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080001d6   0x080001d6   0x00000000   Code   RO          559    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080001d6   0x080001d6   0x00000002   Code   RO          560    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x080001d8   0x080001d8   0x00000000   Code   RO          413    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001d8   0x080001d8   0x00000000   Code   RO          432    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001d8   0x080001d8   0x00000006   Code   RO          444    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001de   0x080001de   0x00000000   Code   RO          434    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001de   0x080001de   0x00000004   Code   RO          435    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001e2   0x080001e2   0x00000000   Code   RO          437    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001e2   0x080001e2   0x00000008   Code   RO          438    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001ea   0x080001ea   0x00000002   Code   RO          461    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001ec   0x080001ec   0x00000000   Code   RO          511    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001ec   0x080001ec   0x00000004   Code   RO          512    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001f0   0x080001f0   0x00000006   Code   RO          513    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001f6   0x080001f6   0x00000002   PAD
    0x080001f8   0x080001f8   0x00000040   Code   RO          346    .text               startup_stm32f10x_hd.o
    0x08000238   0x08000238   0x0000008a   Code   RO          371    .text               c_w.l(rt_memcpy_v6.o)
    0x080002c2   0x080002c2   0x00000006   Code   RO          373    .text               c_w.l(heapauxi.o)
    0x080002c8   0x080002c8   0x00000064   Code   RO          414    .text               c_w.l(rt_memcpy_w.o)
    0x0800032c   0x0800032c   0x0000004a   Code   RO          448    .text               c_w.l(sys_stackheap_outer.o)
    0x08000376   0x08000376   0x00000012   Code   RO          450    .text               c_w.l(exit.o)
    0x08000388   0x08000388   0x00000008   Code   RO          458    .text               c_w.l(libspace.o)
    0x08000390   0x08000390   0x0000000c   Code   RO          521    .text               c_w.l(sys_exit.o)
    0x0800039c   0x0800039c   0x00000002   Code   RO          536    .text               c_w.l(use_no_semi.o)
    0x0800039e   0x0800039e   0x00000000   Code   RO          538    .text               c_w.l(indicate_semi.o)
    0x0800039e   0x0800039e   0x00000002   PAD
    0x080003a0   0x080003a0   0x000000b2   Code   RO           86    .text.ADC1_Config   userfunction.o
    0x08000452   0x08000452   0x00000002   PAD
    0x08000454   0x08000454   0x000000b4   Code   RO          184    .text.Auto_Calibrate_All_Motors  userfunction.o
    0x08000508   0x08000508   0x00000036   Code   RO           14    .text.ClearCmdBuffer  main.o
    0x0800053e   0x0800053e   0x00000002   PAD
    0x08000540   0x08000540   0x00000056   Code   RO            4    .text.Delay_mS      main.o
    0x08000596   0x08000596   0x00000002   PAD
    0x08000598   0x08000598   0x0000005c   Code   RO           16    .text.Delay_uS      main.o
    0x080005f4   0x080005f4   0x00000042   Code   RO          132    .text.GetEncoder_1_Angele  userfunction.o
    0x08000636   0x08000636   0x00000002   PAD
    0x08000638   0x08000638   0x00000042   Code   RO          134    .text.GetEncoder_2_Angele  userfunction.o
    0x0800067a   0x0800067a   0x00000002   PAD
    0x0800067c   0x0800067c   0x0000000c   Code   RO          170    .text.Get_System_MS  userfunction.o
    0x08000688   0x08000688   0x0000016c   Code   RO          245    .text.JSON_ExecuteCommand  json_parser.o
    0x080007f4   0x080007f4   0x000000b6   Code   RO          241    .text.JSON_GetIntValue  json_parser.o
    0x080008aa   0x080008aa   0x00000002   PAD
    0x080008ac   0x080008ac   0x0000018e   Code   RO          237    .text.JSON_GetStringValue  json_parser.o
    0x08000a3a   0x08000a3a   0x00000002   PAD
    0x08000a3c   0x08000a3c   0x00000150   Code   RO          243    .text.JSON_Parse    json_parser.o
    0x08000b8c   0x08000b8c   0x000001c8   Code   RO          231    .text.JSON_StringToCommand  json_parser.o
    0x08000d54   0x08000d54   0x0000003e   Code   RO          313    .text.LCD_SendString  lcd.o
    0x08000d92   0x08000d92   0x00000002   PAD
    0x08000d94   0x08000d94   0x00000106   Code   RO          301    .text.LCD_Send_4BitCmd  lcd.o
    0x08000e9a   0x08000e9a   0x00000002   PAD
    0x08000e9c   0x08000e9c   0x0000019a   Code   RO          299    .text.LCD_Send_Command  lcd.o
    0x08001036   0x08001036   0x00000002   PAD
    0x08001038   0x08001038   0x0000019a   Code   RO          303    .text.LCD_Send_Data  lcd.o
    0x080011d2   0x080011d2   0x00000002   PAD
    0x080011d4   0x080011d4   0x0000007e   Code   RO          305    .text.LCD_Setup     lcd.o
    0x08001252   0x08001252   0x00000002   PAD
    0x08001254   0x08001254   0x00000042   Code   RO          166    .text.Load_Embedded_Config  userfunction.o
    0x08001296   0x08001296   0x00000002   PAD
    0x08001298   0x08001298   0x00000024   Code   RO          150    .text.M2_Hold_Position_Active  userfunction.o
    0x080012bc   0x080012bc   0x0000000a   Code   RO            6    .text.NVIC_EnableIRQ  main.o
    0x080012c6   0x080012c6   0x00000002   PAD
    0x080012c8   0x080012c8   0x0000000c   Code   RO            8    .text.NVIC_SetPriority  main.o
    0x080012d4   0x080012d4   0x00000116   Code   RO          160    .text.Play_Note     userfunction.o
    0x080013ea   0x080013ea   0x00000002   PAD
    0x080013ec   0x080013ec   0x000001bc   Code   RO          162    .text.Play_Piano_Melody  userfunction.o
    0x080015a8   0x080015a8   0x000000c6   Code   RO          263    .text.RCCInit       rcc.o
    0x0800166e   0x0800166e   0x00000002   PAD
    0x08001670   0x08001670   0x00000134   Code   RO          142    .text.Ready_Command  userfunction.o
    0x080017a4   0x080017a4   0x000000ea   Code   RO          154    .text.Return_All_Motors_Home  userfunction.o
    0x0800188e   0x0800188e   0x00000002   PAD
    0x08001890   0x08001890   0x000001b0   Code   RO          106    .text.RotateM1_D14_Position  userfunction.o
    0x08001a40   0x08001a40   0x000001a0   Code   RO          108    .text.RotateM2_D13_Position  userfunction.o
    0x08001be0   0x08001be0   0x000001fe   Code   RO          112    .text.Rotate_M1_CCW  userfunction.o
    0x08001dde   0x08001dde   0x00000002   PAD
    0x08001de0   0x08001de0   0x000001fe   Code   RO          110    .text.Rotate_M1_CW  userfunction.o
    0x08001fde   0x08001fde   0x00000002   PAD
    0x08001fe0   0x08001fe0   0x00000176   Code   RO          116    .text.Rotate_M2_CCW  userfunction.o
    0x08002156   0x08002156   0x00000002   PAD
    0x08002158   0x08002158   0x0000016e   Code   RO          114    .text.Rotate_M2_CW  userfunction.o
    0x080022c6   0x080022c6   0x00000002   PAD
    0x080022c8   0x080022c8   0x0000021a   Code   RO          118    .text.Rotate_M3     userfunction.o
    0x080024e2   0x080024e2   0x00000002   PAD
    0x080024e4   0x080024e4   0x00000168   Code   RO          148    .text.Rotate_M3_Adaptive  userfunction.o
    0x0800264c   0x0800264c   0x00000218   Code   RO          120    .text.Rotate_M4     userfunction.o
    0x08002864   0x08002864   0x0000021c   Code   RO          122    .text.Rotate_M5     userfunction.o
    0x08002a80   0x08002a80   0x000001f4   Code   RO          124    .text.Rotate_M6     userfunction.o
    0x08002c74   0x08002c74   0x00000138   Code   RO          140    .text.Rotate_M6_Max_Power  userfunction.o
    0x08002dac   0x08002dac   0x000000cc   Code   RO          126    .text.Rotate_M6_Step  userfunction.o
    0x08002e78   0x08002e78   0x000001fa   Code   RO          128    .text.Rotate_M6_Step_Safe  userfunction.o
    0x08003072   0x08003072   0x00000002   PAD
    0x08003074   0x08003074   0x000001ca   Code   RO          130    .text.Rotate_M7     userfunction.o
    0x0800323e   0x0800323e   0x00000002   PAD
    0x08003240   0x08003240   0x0000003e   Code   RO           12    .text.SaveReceivedCommand  main.o
    0x0800327e   0x0800327e   0x00000002   PAD
    0x08003280   0x08003280   0x0000004c   Code   RO          104    .text.Send_To_Main  userfunction.o
    0x080032cc   0x080032cc   0x00000008   Code   RO          355    .text.SetSysClock   system_stm32f10x.o
    0x080032d4   0x080032d4   0x00000122   Code   RO          359    .text.SetSysClockTo72  system_stm32f10x.o
    0x080033f6   0x080033f6   0x00000002   PAD
    0x080033f8   0x080033f8   0x00000058   Code   RO          284    .text.SetUp_I2C1    i2c.o
    0x08003450   0x08003450   0x00000686   Code   RO          255    .text.SetupGpioIO   io_gpio.o
    0x08003ad6   0x08003ad6   0x00000002   PAD
    0x08003ad8   0x08003ad8   0x0000008a   Code   RO          273    .text.SetupTimers   timers.o
    0x08003b62   0x08003b62   0x00000002   PAD
    0x08003b64   0x08003b64   0x00000230   Code   RO          164    .text.Show_About_Page  userfunction.o
    0x08003d94   0x08003d94   0x000000ba   Code   RO          152    .text.Show_All_Motors_Status  userfunction.o
    0x08003e4e   0x08003e4e   0x00000002   PAD
    0x08003e50   0x08003e50   0x000000e2   Code   RO          168    .text.Show_Current_Config  userfunction.o
    0x08003f32   0x08003f32   0x00000002   PAD
    0x08003f34   0x08003f34   0x0000007c   Code   RO           94    .text.Signal_3p1D   userfunction.o
    0x08003fb0   0x08003fb0   0x00000066   Code   RO          353    .text.SystemInit    system_stm32f10x.o
    0x08004016   0x08004016   0x00000002   PAD
    0x08004018   0x08004018   0x00000010   Code   RO          271    .text.TIM2_IRQHandler  timers.o
    0x08004028   0x08004028   0x00000022   Code   RO          275    .text.TIM4_IRQHandler  timers.o
    0x0800404a   0x0800404a   0x00000002   PAD
    0x0800404c   0x0800404c   0x000006dc   Code   RO          158    .text.Test_All_Motors_Max_Speed  userfunction.o
    0x08004728   0x08004728   0x0000014a   Code   RO          156    .text.Test_M1_Simple  userfunction.o
    0x08004872   0x08004872   0x00000002   PAD
    0x08004874   0x08004874   0x00000032   Code   RO          144    .text.Timer_Start   userfunction.o
    0x080048a6   0x080048a6   0x00000002   PAD
    0x080048a8   0x080048a8   0x000001a6   Code   RO          146    .text.Timer_Stop_And_Show  userfunction.o
    0x08004a4e   0x08004a4e   0x00000002   PAD
    0x08004a50   0x08004a50   0x00000100   Code   RO           20    .text.USART1_IRQHandler  main.o
    0x08004b50   0x08004b50   0x00000020   Code   RO           22    .text.USART2_IRQHandler  main.o
    0x08004b70   0x08004b70   0x00000002   Code   RO           10    .text.__enable_irq  main.o
    0x08004b72   0x08004b72   0x00000002   PAD
    0x08004b74   0x08004b74   0x00001416   Code   RO            2    .text.main          main.o
    0x08005f8a   0x08005f8a   0x00000002   PAD
    0x08005f8c   0x08005f8c   0x0000006c   Code   RO          233    .text.simple_strcmp  json_parser.o
    0x08005ff8   0x08005ff8   0x00000048   Code   RO          239    .text.simple_strlen  json_parser.o
    0x08006040   0x08006040   0x00000018   Data   RO          215    .rodata..L__const.Play_Piano_Melody.notes  userfunction.o
    0x08006058   0x08006058   0x00000468   Data   RO           76    .rodata.str1.1      main.o
    0x080064c0   0x080064c0   0x0000098c   Data   RO          213    .rodata.str1.1      userfunction.o
    0x08006e4c   0x08006e4c   0x000001ff   Data   RO          247    .rodata.str1.1      json_parser.o
    0x0800704b   0x0800704b   0x00000001   PAD
    0x0800704c   0x0800704c   0x00000020   Data   RO          594    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08007070, Size: 0x000007c8, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08007070   0x00000002   Data   RW           32    .data.M1_PulseWidth  main.o
    0x20000002   0x08007072   0x00000002   Data   RW           31    .data.M1_StepDelay  main.o
    0x20000004   0x08007074   0x00000002   Data   RW           37    .data.M2_ExtraDelay_CCW  main.o
    0x20000006   0x08007076   0x00000002   Data   RW           36    .data.M2_PulseWidth_CCW  main.o
    0x20000008   0x08007078   0x00000002   Data   RW           35    .data.M2_StepDelay_CCW  main.o
    0x2000000a   0x0800707a   0x00000002   Data   RW           34    .data.M2_StepDelay_CW  main.o
    0x2000000c   0x0800707c   0x00000002   Data   RW           47    .data.M3_PulseWidth_uS  main.o
    0x2000000e   0x0800707e   0x00000002   Data   RW           46    .data.M3_StepDelay_uS  main.o
    0x20000010   0x08007080   0x00000002   Data   RW           56    .data.M4_PulseWidth_uS  main.o
    0x20000012   0x08007082   0x00000002   Data   RW           55    .data.M4_StepDelay_uS  main.o
    0x20000014   0x08007084   0x00000002   Data   RW           61    .data.M5_PulseWidth_uS  main.o
    0x20000016   0x08007086   0x00000002   Data   RW           60    .data.M5_StepDelay_uS  main.o
    0x20000018   0x08007088   0x00000002   Data   RW           63    .data.M6_PulseWidth  main.o
    0x2000001a   0x0800708a   0x00000002   Data   RW           62    .data.M6_StepDelay  main.o
    0x2000001c   0x0800708c   0x00000004   PAD
    0x20000020        -       0x00000060   Zero   RW          459    .bss                c_w.l(libspace.o)
    0x20000080        -       0x00000002   Zero   RW           67    .bss.Encoders_Angele  main.o
    0x20000082        -       0x00000002   Zero   RW           70    .bss.M1_Angele      main.o
    0x20000084        -       0x00000002   Zero   RW           71    .bss.M2_Angele      main.o
    0x20000086        -       0x00000001   Zero   RW           73    .bss.M7_Error       main.o
    0x20000087   0x0800708c   0x00000001   PAD
    0x20000088        -       0x00000002   Zero   RW           72    .bss.Rotate_Angele  main.o
    0x2000008a        -       0x00000002   Zero   RW          214    .bss.Rotate_M6_Step_Safe.step_counter  userfunction.o
    0x2000008c        -       0x00000001   Zero   RW           74    .bss.SensorPositionError  main.o
    0x2000008d   0x0800708c   0x00000003   PAD
    0x20000090        -       0x00000004   Zero   RW          220    .bss.g_system_ms_counter  userfunction.o
    0x20000094        -       0x00000004   Zero   RW          218    .bss.g_timer_current_ms  userfunction.o
    0x20000098        -       0x00000001   Zero   RW          219    .bss.g_timer_running  userfunction.o
    0x20000099   0x0800708c   0x00000003   PAD
    0x2000009c        -       0x00000004   Zero   RW          217    .bss.g_timer_start_ms  userfunction.o
    0x200000a0        -       0x000000a0   Zero   RW          222    .bss.motor_stats    userfunction.o
    0x20000140        -       0x00000001   Zero   RW          221    .bss.position_hold_active  userfunction.o
    0x20000141        -       0x00000001   Zero   RW           75    .bss.projectile_number  main.o
    0x20000142        -       0x00000040   Zero   RW           26    .bss.u8_CmdBuffer_1  main.o
    0x20000182        -       0x00000001   Zero   RW           25    .bss.u8_CmdIndex_1  main.o
    0x20000183        -       0x00000001   Zero   RW           28    .bss.u8_CmdNumber   main.o
    0x20000184        -       0x00000040   Zero   RW           27    .bss.u8_ReceivedCommand  main.o
    0x200001c4        -       0x00000001   Zero   RW           24    .bss.u8_Uart1_Cmd   main.o
    0x200001c5   0x0800708c   0x00000003   PAD
    0x200001c8        -       0x00000200   Zero   RW          344    HEAP                startup_stm32f10x_hd.o
    0x200003c8        -       0x00000400   Zero   RW          343    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        88          0          0          0          0       1047   i2c.o
      1670          0          0          0          0       1429   io_gpio.o
      1916         16        511          0          0       5005   json_parser.o
      1270          0          0          0          0       4744   lcd.o
      5748        212       1128         28        142       9457   main.o
       198          0          0          0          0        921   rcc.o
        64         26        304          0       1536        820   startup_stm32f10x_hd.o
       400          0          0          0          0       2973   system_stm32f10x.o
       188          0          0          0          0       1279   timers.o
     12160          4       2468          0        176      21842   userfunction.o

    ----------------------------------------------------------------------
     23776        <USER>       <GROUP>         28       1868      49517   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        74          0          1          0         14          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        94          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o

    ----------------------------------------------------------------------
       560         <USER>          <GROUP>          0         96        732   Library Totals
         8          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       552         16          0          0         96        732   c_w.l

    ----------------------------------------------------------------------
       560         <USER>          <GROUP>          0         96        732   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     24336        274       4444         28       1964      49945   Grand Totals
     24336        274       4444         28       1964      49945   ELF Image Totals
     24336        274       4444         28          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                28780 (  28.11kB)
    Total RW  Size (RW Data + ZI Data)              1992 (   1.95kB)
    Total ROM Size (Code + RO Data + RW Data)      28808 (  28.13kB)

==============================================================================

